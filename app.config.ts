import "dotenv/config";

export default {
  expo: {
    name: "resapp",
    slug: "resapp",
    version: "1.0.0",
    orientation: "portrait",
    icon: "./src/assets/icon.png",
    userInterfaceStyle: "light",
    newArchEnabled: true,
    splash: {
      image: "./src/assets/splash-icon.png",
      resizeMode: "contain",
      backgroundColor: "#ffffff",
    },
    ios: {
      supportsTablet: true,
      bundleIdentifier: "com.anonymous.resapp",
    },
    android: {
      adaptiveIcon: {
        foregroundImage: "./src/assets/adaptive-icon.png",
        backgroundColor: "#ffffff",
      },
      package: "com.anonymous.resapp",
    },
    web: {
      favicon: "./src/assets/favicon.png",
    },
    extra: {
      API_URL: process.env.API_URL,
      API_KEY: process.env.API_KEY,
    },
    plugins: [
      [
        "expo-image-picker",
        {
          photosPermission: {
            title: "Sabino Zibatá",
            message:
              "Permitemos acceder a tu galería para seleccionar imágenes.",
            buttonPositive: "Ok",
          },
        },
      ],
    ],
  },
};
