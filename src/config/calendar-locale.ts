import { LocaleConfig } from "react-native-calendars";

export const configureCalendarLocale = () => {
  LocaleConfig.locales["es"] = {
    monthNames: [
      "<PERSON><PERSON>",
      "<PERSON><PERSON><PERSON>",
      "<PERSON><PERSON>",
      "<PERSON><PERSON><PERSON>",
      "Mayo",
      "<PERSON><PERSON>",
      "<PERSON>",
      "Agosto",
      "Septiembre",
      "Octubre",
      "Noviembre",
      "Diciembre",
    ],
    monthNamesShort: [
      "Ene",
      "Feb",
      "Mar",
      "Abr",
      "May",
      "Jun",
      "Jul",
      "Ago",
      "Sep",
      "Oct",
      "Nov",
      "Dic",
    ],
    dayNames: [
      "Domingo",
      "Lunes",
      "Martes",
      "Miércoles",
      "Jueves",
      "Viernes",
      "Sábado",
    ],
    dayNamesShort: ["Dom", "Lun", "Mar", "Mi<PERSON>", "<PERSON>e", "Vie", "<PERSON>áb"],
    today: "Hoy",
  };
};

LocaleConfig.defaultLocale = "es";
