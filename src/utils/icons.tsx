import { Facility } from "../interfaces/facility";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { theme } from "../theme";

export const getFacilityIcons = (facilityName: Facility["name"]) => {
  if (facilityName === "Alberca")
    return (
      <MaterialCommunityIcons
        name="pool"
        size={theme.fontSizes.lg}
        color={theme.colors.primary}
      />
    );
};
