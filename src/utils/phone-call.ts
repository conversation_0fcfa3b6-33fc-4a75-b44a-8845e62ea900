import { Linking, Alert } from "react-native";

export const handleCall = (phoneNumber: string) => {
  const url = `tel:${phoneNumber}`;
  Linking.canOpenURL(url)
    .then((supported) => {
      if (!supported) {
        Alert.alert("Error", "No se puede hacer la llamada");
      } else {
        return Linking.openURL(url);
      }
    })
    .catch((err) => console.error("Error al intentar llamar:", err));
};
