import { DateTime, Settings } from "luxon";

// Esto puedes hacerlo una sola vez en tu app
Settings.defaultLocale = "es";

export const getGreeting = () => {
  const hour = DateTime.local().hour;

  if (hour < 12) return "Buenos días";
  if (hour < 19) return "Buenas tardes";
  return "Buenas noches";
};

export const getStringTime = (time: string): string => {
  return DateTime.fromHTTP(time).toFormat("HH:mm");
};

export const getStringTimeFromIso = (dateTime: string): string => {
  return DateTime.fromISO(dateTime).toFormat("HH:mm");
};

export const formatDateDMY = (isoDateString: string): string => {
  return DateTime.fromISO(isoDateString).toFormat("dd MMM yy");
};

export const shortDaysOfWeek = [
  "Dom", // 0
  "Lun", // 1
  "Mar", // 2
  "<PERSON><PERSON>", // 3
  "<PERSON><PERSON>", // 4
  "Vie", // 5
  "Sáb", // 6
];

export const longDaysOfWeek = [
  "Domingo", // 0
  "Lunes", // 1
  "Martes", // 2
  "Miércoles", // 3
  "Jueves", // 4
  "Viernes", // 5
  "Sábado", // 6
];

export const shortMonths = [
  "Ene", // 0
  "Feb", // 1
  "Mar", // 2
  "Abr", // 3
  "May", // 4
  "Jun", // 5
  "Jul", // 6
  "Ago", // 7
  "Sep", // 8
  "Oct", // 9
  "Nov", // 10
  "Dic", // 11
];
export const getDateDMY = (isoDateString: string): string => {
  const date = DateTime.fromISO(isoDateString);
  const day = date.day;
  const month = shortMonths[date.month - 1];
  const year = date.year;

  return `${day} ${month} ${year}`;
};
export const getDateDMYWithTime = (isoDateString: string): string => {
  const date = DateTime.fromISO(isoDateString);
  const day = date.day;
  const month = shortMonths[date.month - 1];
  const year = date.year;
  const hour = date.hour;
  const minute = date.minute;

  return `${day} ${month} ${year} ${hour}:${minute}`;
};

export const getDateFromJSDate = (date: Date) => {
  return DateTime.fromJSDate(new Date(date))
    .setLocale("es")
    .toLocaleString(DateTime.DATE_MED);
};

export const getTimeForJSDate = (date: string) => {
  return DateTime.fromJSDate(new Date(date)).toFormat("HH:mm");
};

export const getDayNumber = (isoDate: string) => {
  return DateTime.fromISO(isoDate).day;
};

export const getDayName = (isoDate: string) => {
  return longDaysOfWeek[DateTime.fromISO(isoDate).weekday];
};

export const getToday = () => {
  return DateTime.local().toFormat("yyyy-MM-dd");
};

export const getCurrentTime = () => {
  return DateTime.local();
};

export const getCurentDay = () => {
  return DateTime.local().weekday;
};

//Function to get if datetime is between to dates,
// the dates are in http format and we need to consider just the time
export const isDateTimeBetween = (
  dateTime: DateTime,
  startDate: string,
  endDate: string
) => {
  const start = DateTime.fromHTTP(startDate).toFormat("HH:mm");
  const end = DateTime.fromHTTP(endDate).toFormat("HH:mm");
  const date = dateTime.toFormat("HH:mm");
  return date >= start && date <= end;
};

export const formatDateTime = (dateTime: string) => {
  return `${formatDateDMY(dateTime)} ${getStringTimeFromIso(dateTime)}`;
};
