import {
  createContext,
  useContext,
  useEffect,
  useState,
  useCallback,
  useMemo,
  ReactNode,
} from "react";
import { hoaClient } from "../api/axios-clients";
import { AuthResponse } from "../types/auth";
import { setLogoutHandler } from "../api/auth/auth-handle";

interface AuthContextType {
  user: AuthResponse | null;
  logout: () => Promise<void>;
  isLoading: boolean;
  checkSession: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<AuthResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const logout = useCallback(async () => {
    try {
      await hoaClient.post("/auth/logout");
    } catch (e) {
      console.warn("Error cerrando sesión", e);
    } finally {
      setUser(null);
    }
  }, []);

  const checkSession = useCallback(async () => {
    try {
      console.log("En check session");
      const response = await hoaClient.get<{
        authenticated: boolean;
        user: AuthResponse;
      }>("/auth/check");

      if (response.data.authenticated) {
        console.log("Sesión válida:");
        setUser(response.data.user);
      } else {
        throw new Error("No autenticado");
      }
    } catch {
      try {
        console.log("Intentando refresh-token...");
        const response = await hoaClient.post<{
          authenticated: boolean;
          user: AuthResponse;
        }>("/auth/refresh-token");
        console.log("Token refrescado:", response.data);
        setUser(response.data.user);
      } catch (err) {
        console.warn("Falló el refresh-token");
        console.log(err);
        await logout();
      }
    } finally {
      setIsLoading(false);
    }
  }, [logout]);

  useEffect(() => {
    checkSession();
  }, [checkSession]);

  useEffect(() => {
    setLogoutHandler(logout);
  }, [logout]);

  const contextValue = useMemo(
    () => ({ user, logout, isLoading, userId: user?.sub, checkSession }),
    [user, logout, isLoading]
  );

  return (
    <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
  );
};

export const useAuthContext = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuthContext debe usarse dentro de AuthProvider");
  }
  return context;
};
