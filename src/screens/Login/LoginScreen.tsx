import {
  SafeAreaView,
  TouchableWithoutFeedback,
  Keyboard,
  Image,
  StyleSheet,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import sabino from "../../assets/sabino.jpeg";
import { LoginForm } from "../../components/forms/LoginForm";
import { Col } from "../../components/main/Col";

export const LoginScreen: React.FC = () => {
  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <LinearGradient
        colors={["#1B4959", "#DFD6C6"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 2.5, y: 0 }}
        style={{ flex: 1 }}
      >
        <SafeAreaView style={styles.area}>
          <Col style={styles.container}>
            <Image source={sabino} resizeMode="contain" style={styles.logo} />
            <LoginForm />
          </Col>
        </SafeAreaView>
      </LinearGradient>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  area: {
    flex: 1,
  },
  container: {
    flex: 1,
    paddingLeft: 20,
    paddingRight: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  logo: {
    maxWidth: "60%",
  },
});
