import { StyleSheet, Text, ScrollView, View } from "react-native";
import { GradientView } from "../../components/layouts/GradientView";
import { Card } from "../../components/main/Card";
import { Row } from "../../components/main/Row";
import { Col } from "../../components/main/Col";
import { theme } from "../../theme";
import { useCachedQuery } from "../../hooks/useCachedQueries";
import { Loading } from "../../components/Loading";
import { PropertyResidentsRouteProp } from "../../navigation/types";
import { useRoute } from "@react-navigation/native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { SmartAvatar } from "../../components/main/Avatar/SmartAvatar";
import { getRoleData } from "../../utils/convertions";
import { User } from "../../interfaces/user";

export const PropertyResidentsScreen: React.FC = () => {
  const route = useRoute<PropertyResidentsRouteProp>();
  const { propertyId } = route.params || {};

  const { data: resdentsData, isLoading } = useCachedQuery<User[]>(
    `mobile/property/${propertyId}/residents`
  );

  const residents = resdentsData ?? [];

  if (isLoading) return <Loading />;

  return (
    <GradientView firstLineText="Residentes">
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Resumen */}
        <Card style={styles.summaryCard}>
          <Row align="center" style={styles.summaryRow}>
            <MaterialCommunityIcons
              name="account-group"
              size={32}
              color={theme.colors.primary}
            />
            <Col style={styles.summaryTextContainer}>
              <Text style={styles.summaryNumber}>{residents.length}</Text>
              <Text style={styles.summaryLabel}>
                {residents.length === 1 ? "Residente" : "Residentes"}
              </Text>
            </Col>
          </Row>
        </Card>

        {/* Lista de residentes */}
        {residents.length === 0 ? (
          <Card style={styles.noDataCard}>
            <Text style={styles.noDataText}>No hay residentes registrados</Text>
          </Card>
        ) : (
          residents.map((resident) => {
            const primaryRole = resident.roles[0];
            const roleData = getRoleData(primaryRole?.name || "");

            return (
              <View key={resident.id}>
                <Card style={styles.residentCard}>
                  <Row align="center">
                    <SmartAvatar
                      name={`${resident.firstName} ${resident.paternalLastName}`}
                      size={50}
                    />
                    <Col style={styles.residentInfo}>
                      <Text style={styles.residentName}>
                        {resident.firstName} {resident.paternalLastName}{" "}
                        {resident.maternalLastName}
                      </Text>
                      <Row align="center" style={styles.detailsRow}>
                        <MaterialCommunityIcons
                          name="email"
                          size={14}
                          color={theme.colors.gray500}
                        />
                        <Text style={styles.residentEmail}>
                          {resident.email}
                        </Text>
                      </Row>
                      {resident.phone !== undefined && (
                        <Row align="center" style={styles.detailsRow}>
                          <MaterialCommunityIcons
                            name="phone"
                            size={14}
                            color={theme.colors.gray500}
                          />
                          <Text style={styles.residentPhone}>
                            {resident.phone}
                          </Text>
                        </Row>
                      )}
                      {primaryRole && (
                        <Row align="center" style={styles.detailsRow}>
                          <MaterialCommunityIcons
                            name={roleData.icon}
                            size={14}
                            color={roleData.color}
                          />
                          <Text
                            style={[
                              styles.residentRole,
                              { color: roleData.color },
                            ]}
                          >
                            {roleData.label}
                          </Text>
                        </Row>
                      )}
                    </Col>
                  </Row>
                </Card>
              </View>
            );
          })
        )}
      </ScrollView>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  summaryCard: {
    marginBottom: theme.spacing.md,
    flexDirection: "column",
  },
  summaryRow: {
    padding: theme.spacing.md,
  },
  summaryTextContainer: {
    marginLeft: theme.spacing.md,
    alignItems: "center",
  },
  summaryNumber: {
    fontSize: theme.fontSizes.xxl,
    fontWeight: "700",
    color: theme.colors.primary,
  },
  summaryLabel: {
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    fontWeight: "500",
  },
  noDataCard: {
    padding: theme.spacing.xl,
    alignItems: "center",
  },
  noDataText: {
    textAlign: "center",
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    fontStyle: "italic",
  },
  residentCard: {
    marginBottom: theme.spacing.md,
    padding: theme.spacing.md,
    flexDirection: "column",
  },
  residentInfo: {
    marginLeft: theme.spacing.md,
    flex: 1,
  },
  residentName: {
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
    color: theme.colors.black,
    marginBottom: theme.spacing.xs,
  },
  detailsRow: {
    marginBottom: theme.spacing.xs,
  },
  residentEmail: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
    marginLeft: theme.spacing.xs,
  },
  residentPhone: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
    marginLeft: theme.spacing.xs,
  },
  residentRole: {
    fontSize: theme.fontSizes.sm,
    fontWeight: "500",
    marginLeft: theme.spacing.xs,
  },
});
