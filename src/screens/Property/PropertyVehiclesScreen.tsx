import { StyleSheet, Text, View, ScrollView } from "react-native";
import { GradientView } from "../../components/layouts/GradientView";
import { Card } from "../../components/main/Card";
import { Row } from "../../components/main/Row";
import { Col } from "../../components/main/Col";
import { theme } from "../../theme";
import { useCachedQuery } from "../../hooks/useCachedQueries";
import { Loading } from "../../components/Loading";
import { PropertyVehiclesRouteProp } from "../../navigation/types";
import { useRoute } from "@react-navigation/native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { Vehicle } from "../../interfaces/vehicle";

export const PropertyVehiclesScreen: React.FC = () => {
  const route = useRoute<PropertyVehiclesRouteProp>();
  const { propertyId } = route.params || {};

  const { data: vehiclesData, isLoading } = useCachedQuery<Vehicle[]>(
    `mobile/property/${propertyId}/vehicles`
  );

  const vehicles = vehiclesData ?? [];

  if (isLoading) return <Loading />;

  return (
    <GradientView firstLineText="Vehículos">
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Resumen */}
        <Card style={styles.summaryCard}>
          <Row align="center" style={styles.summaryRow}>
            <MaterialCommunityIcons
              name="car"
              size={32}
              color={theme.colors.primary}
            />
            <Col style={styles.summaryTextContainer}>
              <Text style={styles.summaryNumber}>{vehicles.length}</Text>
              <Text style={styles.summaryLabel}>
                {vehicles.length === 1 ? "Vehículo" : "Vehículos"}
              </Text>
            </Col>
          </Row>
        </Card>

        {/* Lista de vehículos */}
        {vehicles.length === 0 ? (
          <Card style={styles.noDataCard}>
            <Text style={styles.noDataText}>No hay vehículos registrados</Text>
          </Card>
        ) : (
          vehicles.map((vehicle) => (
            <View key={vehicle.id}>
              <Card style={styles.vehicleCard}>
                <Row align="center">
                  <View style={styles.iconContainer}>
                    <MaterialCommunityIcons
                      name="car"
                      size={32}
                      color={theme.colors.primary}
                    />
                  </View>
                  <Col style={styles.vehicleInfo}>
                    <Text style={styles.vehicleBrandModel}>
                      {vehicle.brand} {vehicle.model}
                    </Text>
                    <Row align="center" style={styles.detailsRow}>
                      <MaterialCommunityIcons
                        name="palette"
                        size={14}
                        color={theme.colors.gray500}
                      />
                      <Text style={styles.vehicleColor}>{vehicle.color}</Text>
                    </Row>
                    <Row align="center" style={styles.detailsRow}>
                      <MaterialCommunityIcons
                        name="card-text"
                        size={14}
                        color={theme.colors.gray500}
                      />
                      <Text style={styles.vehiclePlate}>{vehicle.plate}</Text>
                    </Row>
                  </Col>
                  <View style={styles.plateContainer}>
                    <Text style={styles.plateText}>{vehicle.plate}</Text>
                  </View>
                </Row>
              </Card>
            </View>
          ))
        )}
      </ScrollView>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  summaryCard: {
    marginBottom: theme.spacing.md,
    flexDirection: "column",
  },
  summaryRow: {
    padding: theme.spacing.md,
  },
  summaryTextContainer: {
    marginLeft: theme.spacing.md,
    alignItems: "center",
  },
  summaryNumber: {
    fontSize: theme.fontSizes.xxl,
    fontWeight: "700",
    color: theme.colors.primary,
  },
  summaryLabel: {
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    fontWeight: "500",
  },
  noDataCard: {
    padding: theme.spacing.xl,
    alignItems: "center",
  },
  noDataText: {
    textAlign: "center",
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    fontStyle: "italic",
  },
  vehicleCard: {
    marginBottom: theme.spacing.md,
    padding: theme.spacing.md,
    flexDirection: "column",
  },
  iconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: `${theme.colors.primary}15`,
    alignItems: "center",
    justifyContent: "center",
  },
  vehicleInfo: {
    marginLeft: theme.spacing.md,
    flex: 1,
  },
  vehicleBrandModel: {
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
    color: theme.colors.black,
    marginBottom: theme.spacing.xs,
  },
  detailsRow: {
    marginBottom: theme.spacing.xs,
  },
  vehicleColor: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
    marginLeft: theme.spacing.xs,
  },
  vehiclePlate: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
    marginLeft: theme.spacing.xs,
  },
  plateContainer: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.radii.sm,
    minWidth: 80,
    alignItems: "center",
  },
  plateText: {
    color: theme.colors.white,
    fontSize: theme.fontSizes.sm,
    fontWeight: "700",
    letterSpacing: 1,
  },
});
