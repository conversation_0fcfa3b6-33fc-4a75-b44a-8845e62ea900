import { useState, useMemo } from "react";
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
} from "react-native";
import { GradientView } from "../../components/layouts/GradientView";
import { Card } from "../../components/main/Card";
import { Row } from "../../components/main/Row";
import { Col } from "../../components/main/Col";
import { theme } from "../../theme";
import { useCachedQuery } from "../../hooks/useCachedQueries";
import { Me } from "../../interfaces/me";
import { InfractionSeverity } from "../../interfaces/infraction";
import { formatDateDMY } from "../../utils/date-time.utils";
import { Loading } from "../../components/Loading";
import {
  PropertyInfractionsRouteProp,
  PropertyStackNavigationProp,
} from "../../navigation/types";
import { useRoute, useNavigation } from "@react-navigation/native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { getSeverityData } from "../../utils/convertions";
import { PROPERTY_SCREENS } from "../../navigation/constants";

type InfractionFilter = InfractionSeverity | "ALL";

const getNoDataMessage = (filter: InfractionFilter): string => {
  switch (filter) {
    case "ALL":
      return "No hay infracciones registradas";
    case InfractionSeverity.SEVERE:
      return "No hay infracciones graves";
    case InfractionSeverity.MODERATE:
      return "No hay infracciones moderadas";
    case InfractionSeverity.MINOR:
      return "No hay infracciones menores";
    default:
      return "No hay infracciones registradas";
  }
};

export const PropertyInfractionsScreen: React.FC = () => {
  const route = useRoute<PropertyInfractionsRouteProp>();
  const navigation = useNavigation<PropertyStackNavigationProp>();
  const { infractions, property } = route.params || {};

  const [selectedFilter, setSelectedFilter] = useState<InfractionFilter>(
    InfractionSeverity.SEVERE
  );

  const { data: userData, isLoading } = useCachedQuery<Me>(`mobile/me`);

  // Filtrar infracciones según el filtro seleccionado
  const filteredInfractions = useMemo(() => {
    if (selectedFilter === "ALL") return infractions;
    return infractions.filter(
      (infraction) => infraction.severity === selectedFilter
    );
  }, [infractions, selectedFilter]);

  if (isLoading) return <Loading />;
  if (!userData) return <Text>Sin datos</Text>;

  // Estadísticas
  const severeInfractions = infractions.filter(
    (i) => i.severity === InfractionSeverity.SEVERE
  );
  const moderateInfractions = infractions.filter(
    (i) => i.severity === InfractionSeverity.MODERATE
  );
  const minorInfractions = infractions.filter(
    (i) => i.severity === InfractionSeverity.MINOR
  );

  // Ordenar infracciones por fecha (más recientes primero)
  const sortedInfractions = [...filteredInfractions].sort(
    (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
  );

  return (
    <GradientView
      firstLineText="Infracciones"
      secondLineText={property?.address ?? "Mi Propiedad"}
    >
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        <Card style={styles.summaryCard}>
          <Row align="center" style={styles.summaryRow}>
            <Col>
              <TouchableOpacity
                onPress={() => setSelectedFilter(InfractionSeverity.SEVERE)}
                activeOpacity={0.7}
                style={styles.summaryItem}
              >
                <MaterialCommunityIcons
                  name="alert-octagon"
                  size={24}
                  color={theme.colors.error}
                />
                <Text style={styles.summaryNumber}>
                  {severeInfractions.length}
                </Text>
                <Text style={styles.summaryLabel}>Graves</Text>
              </TouchableOpacity>
            </Col>

            <Col>
              <TouchableOpacity
                onPress={() => setSelectedFilter(InfractionSeverity.MODERATE)}
                activeOpacity={0.7}
                style={styles.summaryItem}
              >
                <MaterialCommunityIcons
                  name="alert"
                  size={24}
                  color={theme.colors.warning}
                />
                <Text style={styles.summaryNumber}>
                  {moderateInfractions.length}
                </Text>
                <Text style={styles.summaryLabel}>Moderadas</Text>
              </TouchableOpacity>
            </Col>

            <Col>
              <TouchableOpacity
                onPress={() => setSelectedFilter(InfractionSeverity.MINOR)}
                activeOpacity={0.7}
                style={styles.summaryItem}
              >
                <MaterialCommunityIcons
                  name="information"
                  size={24}
                  color={theme.colors.success}
                />
                <Text style={styles.summaryNumber}>
                  {minorInfractions.length}
                </Text>
                <Text style={styles.summaryLabel}>Menores</Text>
              </TouchableOpacity>
            </Col>

            <Col>
              <TouchableOpacity
                onPress={() => setSelectedFilter("ALL")}
                activeOpacity={0.7}
                style={styles.summaryItem}
              >
                <MaterialCommunityIcons
                  name="view-list-outline"
                  size={24}
                  color={theme.colors.gray500}
                />
                <Text style={styles.summaryNumber}>{infractions.length}</Text>
                <Text style={styles.summaryLabel}>Todas</Text>
              </TouchableOpacity>
            </Col>
          </Row>
        </Card>

        {/* Lista de infracciones */}
        {filteredInfractions.length === 0 ? (
          <Card style={styles.noDataCard}>
            <Text style={styles.noDataText}>
              {getNoDataMessage(selectedFilter)}
            </Text>
          </Card>
        ) : (
          sortedInfractions.map((infraction) => {
            const severityData = getSeverityData(infraction.severity);
            return (
              <TouchableOpacity
                key={infraction.id}
                onPress={() =>
                  navigation.navigate(PROPERTY_SCREENS.INFRACTION_DETAIL, {
                    infraction,
                  })
                }
                activeOpacity={0.7}
              >
                <Card style={styles.infractionCard}>
                  <Row align="flex-start">
                    <View
                      style={[
                        styles.severityIndicator,
                        {
                          backgroundColor: `${severityData.color}20`,
                        },
                      ]}
                    >
                      <MaterialCommunityIcons
                        name={severityData.icon}
                        size={24}
                        color={severityData.color}
                      />
                    </View>
                    <Col style={styles.infractionInfo}>
                      <Row align="center" style={styles.headerRow}>
                        <Text
                          style={styles.infractionDescription}
                          numberOfLines={2}
                        >
                          {infraction.description}
                        </Text>
                        <View
                          style={[
                            styles.severityChip,
                            {
                              backgroundColor: severityData.color,
                            },
                          ]}
                        >
                          <Text style={styles.severityText}>
                            {severityData.label}
                          </Text>
                        </View>
                      </Row>
                      <Row align="center" style={styles.dateRow}>
                        <MaterialCommunityIcons
                          name="calendar"
                          size={14}
                          color={theme.colors.gray500}
                        />
                        <Text style={styles.dateText}>
                          Fecha: {formatDateDMY(infraction.date)}
                        </Text>
                      </Row>
                    </Col>
                    <MaterialCommunityIcons
                      name="chevron-right"
                      size={theme.fontSizes.lg}
                      color={theme.colors.gray500}
                    />
                  </Row>
                </Card>
              </TouchableOpacity>
            );
          })
        )}
      </ScrollView>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  summaryCard: {
    flexDirection: "column",
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.lg,
  },
  summaryRow: {
    justifyContent: "space-around",
    width: "100%",
    marginBottom: theme.spacing.md,
  },
  summaryItem: {
    alignItems: "center",
    flex: 1,
    paddingVertical: theme.spacing.sm,
  },
  summaryNumber: {
    fontSize: theme.fontSizes.xl,
    fontWeight: "700",
    color: theme.colors.black,
    marginTop: theme.spacing.xs,
  },
  summaryLabel: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    textAlign: "center",
    marginTop: 2,
  },
  noDataCard: {
    margin: theme.spacing.lg,
    padding: theme.spacing.xl,
    alignItems: "center",
  },
  noDataText: {
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    textAlign: "center",
    fontStyle: "italic",
  },
  infractionCard: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginHorizontal: theme.spacing.lg,
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.md,
  },
  severityIndicator: {
    width: 48,
    height: 48,
    borderRadius: theme.radii.lg,
    alignItems: "center",
    justifyContent: "center",
    marginRight: theme.spacing.md,
  },
  infractionInfo: {
    flex: 1,
  },
  headerRow: {
    justifyContent: "space-between",
    marginBottom: theme.spacing.sm,
    alignItems: "flex-start",
  },
  infractionDescription: {
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
    color: theme.colors.black,
    flex: 1,
    marginRight: theme.spacing.sm,
    lineHeight: 20,
  },
  severityChip: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.radii.sm,
  },
  severityText: {
    fontSize: theme.fontSizes.xs,
    fontWeight: "600",
    color: theme.colors.white,
    textTransform: "uppercase",
  },
  dateRow: {
    marginTop: theme.spacing.xs,
  },
  dateText: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    marginLeft: 6,
  },
});
