import { useState, useMemo } from "react";
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
} from "react-native";
import { GradientView } from "../../components/layouts/GradientView";
import { Card } from "../../components/main/Card";
import { Row } from "../../components/main/Row";
import { Col } from "../../components/main/Col";
import { theme } from "../../theme";
import { useCachedQuery } from "../../hooks/useCachedQueries";
import { Me } from "../../interfaces/me";
import { ReservationStatus } from "../../interfaces/reservation";
import {
  formatDateDMY,
  getStringTimeFromIso,
} from "../../utils/date-time.utils";
import { Loading } from "../../components/Loading";
import {
  PropertyReservationsRouteProp,
  PropertyStackNavigationProp,
} from "../../navigation/types";
import { useRoute, useNavigation } from "@react-navigation/native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { getReservationStatusData } from "../../utils/convertions";
import { PROPERTY_SCREENS } from "../../navigation/constants";

type ReservationFilter = ReservationStatus | "ALL";

const getNoDataMessage = (filter: ReservationFilter): string => {
  switch (filter) {
    case "ALL":
      return "No hay reservaciones registradas";
    case ReservationStatus.PENDING:
      return "No hay reservaciones pendientes";
    case ReservationStatus.APPROVED:
      return "No hay reservaciones aprobadas";
    case ReservationStatus.REJECTED:
      return "No hay reservaciones rechazadas";
    default:
      return "No hay reservaciones registradas";
  }
};

const isReservationToday = (startDateTime: string): boolean => {
  const today = new Date();
  const reservationDate = new Date(startDateTime);
  return (
    today.getDate() === reservationDate.getDate() &&
    today.getMonth() === reservationDate.getMonth() &&
    today.getFullYear() === reservationDate.getFullYear()
  );
};

export const PropertyReservationsScreen: React.FC = () => {
  const route = useRoute<PropertyReservationsRouteProp>();
  const navigation = useNavigation<PropertyStackNavigationProp>();
  const { reservations, property } = route.params || {};

  const [selectedFilter, setSelectedFilter] = useState<ReservationFilter>(
    ReservationStatus.PENDING
  );

  const { data: userData, isLoading } = useCachedQuery<Me>(`mobile/me`);

  // Filtrar reservaciones según el filtro seleccionado
  const filteredReservations = useMemo(() => {
    if (selectedFilter === "ALL") return reservations;
    return reservations.filter(
      (reservation) => reservation.status === selectedFilter
    );
  }, [reservations, selectedFilter]);

  if (isLoading) return <Loading />;
  if (!userData) return <Text>Sin datos</Text>;

  // Estadísticas
  const pendingReservations = reservations.filter(
    (r) => r.status === ReservationStatus.PENDING
  );
  const approvedReservations = reservations.filter(
    (r) => r.status === ReservationStatus.APPROVED
  );
  const rejectedReservations = reservations.filter(
    (r) => r.status === ReservationStatus.REJECTED
  );

  // Ordenar reservaciones por fecha (más próximas primero)
  const sortedReservations = [...filteredReservations].sort(
    (a, b) =>
      new Date(a.startDateTime).getTime() - new Date(b.startDateTime).getTime()
  );

  return (
    <GradientView
      firstLineText="Reservaciones"
      secondLineText={property?.address ?? "Mi Propiedad"}
    >
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        <Card style={styles.summaryCard}>
          <Row align="center" style={styles.summaryRow}>
            <Col>
              <TouchableOpacity
                onPress={() => setSelectedFilter(ReservationStatus.PENDING)}
                activeOpacity={0.7}
                style={styles.summaryItem}
              >
                <MaterialCommunityIcons
                  name="clock-outline"
                  size={24}
                  color={theme.colors.warning}
                />
                <Text style={styles.summaryNumber}>
                  {pendingReservations.length}
                </Text>
                <Text style={styles.summaryLabel}>Pendientes</Text>
              </TouchableOpacity>
            </Col>

            <Col>
              <TouchableOpacity
                onPress={() => setSelectedFilter(ReservationStatus.APPROVED)}
                activeOpacity={0.7}
                style={styles.summaryItem}
              >
                <MaterialCommunityIcons
                  name="check-circle"
                  size={24}
                  color={theme.colors.success}
                />
                <Text style={styles.summaryNumber}>
                  {approvedReservations.length}
                </Text>
                <Text style={styles.summaryLabel}>Aprobadas</Text>
              </TouchableOpacity>
            </Col>

            <Col>
              <TouchableOpacity
                onPress={() => setSelectedFilter(ReservationStatus.REJECTED)}
                activeOpacity={0.7}
                style={styles.summaryItem}
              >
                <MaterialCommunityIcons
                  name="close-circle"
                  size={24}
                  color={theme.colors.error}
                />
                <Text style={styles.summaryNumber}>
                  {rejectedReservations.length}
                </Text>
                <Text style={styles.summaryLabel}>Rechazadas</Text>
              </TouchableOpacity>
            </Col>

            <Col>
              <TouchableOpacity
                onPress={() => setSelectedFilter("ALL")}
                activeOpacity={0.7}
                style={styles.summaryItem}
              >
                <MaterialCommunityIcons
                  name="view-list-outline"
                  size={24}
                  color={theme.colors.gray500}
                />
                <Text style={styles.summaryNumber}>{reservations.length}</Text>
                <Text style={styles.summaryLabel}>Todas</Text>
              </TouchableOpacity>
            </Col>
          </Row>
        </Card>

        {/* Lista de reservaciones */}
        {filteredReservations.length === 0 ? (
          <Card style={styles.noDataCard}>
            <Text style={styles.noDataText}>
              {getNoDataMessage(selectedFilter)}
            </Text>
          </Card>
        ) : (
          sortedReservations.map((reservation) => {
            const isToday = isReservationToday(reservation.startDateTime);
            const reservationStatusData = getReservationStatusData(
              reservation.status
            );

            return (
              <TouchableOpacity
                key={reservation.id}
                onPress={() =>
                  navigation.navigate(PROPERTY_SCREENS.RESERVATION_DETAIL, {
                    reservation,
                  })
                }
                activeOpacity={0.7}
              >
                <Card
                  style={[styles.reservationCard, isToday && styles.todayCard]}
                >
                  <Row align="flex-start">
                    <View
                      style={[
                        styles.facilityIndicator,
                        {
                          backgroundColor: `${reservationStatusData.color}20`,
                        },
                      ]}
                    >
                      <MaterialCommunityIcons
                        name={reservationStatusData.icon}
                        size={24}
                        color={reservationStatusData.color}
                      />
                    </View>
                    <Col style={styles.reservationInfo}>
                      <Row align="center" style={styles.headerRow}>
                        <Text style={styles.facilityName} numberOfLines={1}>
                          {reservation.facility.name}
                        </Text>
                        <View
                          style={[
                            styles.statusChip,
                            {
                              backgroundColor: reservationStatusData.color,
                            },
                          ]}
                        >
                          <MaterialCommunityIcons
                            name={reservationStatusData.icon}
                            size={12}
                            color={theme.colors.white}
                          />
                          <Text style={styles.statusText}>
                            {reservationStatusData.label}
                          </Text>
                        </View>
                      </Row>

                      <Row align="center" style={styles.detailRow}>
                        <MaterialCommunityIcons
                          name="calendar"
                          size={16}
                          color={theme.colors.gray500}
                        />
                        <Text style={styles.dateText}>
                          {formatDateDMY(reservation.startDateTime)}
                        </Text>
                      </Row>

                      <Row align="center" style={styles.detailRow}>
                        <MaterialCommunityIcons
                          name="clock"
                          size={16}
                          color={theme.colors.gray500}
                        />
                        <Text style={styles.timeText}>
                          {getStringTimeFromIso(reservation.startDateTime)} -{" "}
                          {getStringTimeFromIso(reservation.endDateTime)}
                        </Text>
                      </Row>

                      <Row align="center" style={styles.detailRow}>
                        <MaterialCommunityIcons
                          name="account-multiple"
                          size={16}
                          color={theme.colors.gray500}
                        />
                        <Text style={styles.peopleText}>
                          {reservation.amountOfPeople}{" "}
                          {reservation.amountOfPeople === 1
                            ? "persona"
                            : "personas"}
                        </Text>
                      </Row>

                      {reservation.deniedReason && (
                        <View style={styles.deniedReasonContainer}>
                          <MaterialCommunityIcons
                            name="information"
                            size={14}
                            color={theme.colors.error}
                          />
                          <Text
                            style={styles.deniedReasonText}
                            numberOfLines={2}
                          >
                            {reservation.deniedReason}
                          </Text>
                        </View>
                      )}
                    </Col>
                    <MaterialCommunityIcons
                      name="chevron-right"
                      size={theme.fontSizes.lg}
                      color={theme.colors.gray500}
                    />
                  </Row>
                </Card>
              </TouchableOpacity>
            );
          })
        )}
      </ScrollView>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  summaryCard: {
    flexDirection: "column",
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.lg,
  },
  summaryRow: {
    justifyContent: "space-around",
    width: "100%",
    marginBottom: theme.spacing.md,
  },
  summaryItem: {
    alignItems: "center",
    flex: 1,
    paddingVertical: theme.spacing.sm,
  },
  summaryNumber: {
    fontSize: theme.fontSizes.xl,
    fontWeight: "700",
    color: theme.colors.black,
    marginTop: theme.spacing.xs,
  },
  summaryLabel: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    textAlign: "center",
    marginTop: 2,
  },
  noDataCard: {
    margin: theme.spacing.lg,
    padding: theme.spacing.xl,
    alignItems: "center",
  },
  noDataText: {
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    textAlign: "center",
    fontStyle: "italic",
  },
  reservationCard: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginHorizontal: theme.spacing.lg,
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.md,
  },
  todayCard: {
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.primary,
    backgroundColor: `${theme.colors.primary}05`,
  },
  facilityIndicator: {
    width: 48,
    height: 48,
    borderRadius: theme.radii.lg,
    alignItems: "center",
    justifyContent: "center",
    marginRight: theme.spacing.md,
  },
  reservationInfo: {
    flex: 1,
  },
  headerRow: {
    justifyContent: "space-between",
    marginBottom: theme.spacing.sm,
  },
  facilityName: {
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
    color: theme.colors.black,
    flex: 1,
    marginRight: theme.spacing.sm,
  },
  statusChip: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.radii.sm,
  },
  statusText: {
    fontSize: theme.fontSizes.xs,
    fontWeight: "600",
    color: theme.colors.white,
    marginLeft: 4,
    textTransform: "capitalize",
  },
  detailRow: {
    marginBottom: theme.spacing.xs,
  },
  dateText: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
    marginLeft: 6,
  },
  timeText: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
    marginLeft: 6,
  },
  peopleText: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
    marginLeft: 6,
  },
  deniedReasonContainer: {
    flexDirection: "row",
    alignItems: "flex-start",
    backgroundColor: `${theme.colors.error}10`,
    padding: theme.spacing.sm,
    borderRadius: theme.radii.sm,
    marginTop: theme.spacing.sm,
  },
  deniedReasonText: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.error,
    marginLeft: 6,
    flex: 1,
    lineHeight: 16,
  },
});
