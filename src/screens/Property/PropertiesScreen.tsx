import { StyleSheet, Text } from "react-native";
import { useState } from "react";
import { GradientView } from "../../components/layouts/GradientView";
import { theme } from "../../theme/theme";
import { useCachedQuery } from "../../hooks/useCachedQueries";
import { ScrollView } from "react-native-gesture-handler";
import { PropertySelector } from "../../components/Property/PropertySelector";
import { ComplaintsSection } from "../../components/Sections/ComplaintsSection";
import { MaintenanceIssueReportsSection } from "../../components/Sections/MaintenanceIssueReportsSection";

import { Loading } from "../../components/Loading";
import { PropertyInfoSection } from "../../components/Property/PropertyInfoSection";
import { FinesSection } from "../../components/Property/FinesSection";
import { InfractionsSection } from "../../components/Property/InfractionsSection";
import { ReservationsSection } from "../../components/Property/ReservationsSection";
import { PartialProperty } from "../../interfaces/property";

export const PropertiesScreen: React.FC = () => {
  const {
    data: properties,
    isLoading,
    error,
  } = useCachedQuery<PartialProperty[]>(`mobile/property`);
  const [selectedPropertyId, setSelectedPropertyId] = useState<string | null>(
    null
  );

  if (isLoading) return <Loading />;
  if (error) return <Text>Error al cargar los datos</Text>;
  if (!properties) return <Text>Sin datos</Text>;

  // Si no hay propiedades
  if (properties.length === 0) {
    return (
      <GradientView firstLineText="Mi propiedad">
        <ScrollView>
          <Text style={styles.noDataText}>
            No tienes propiedades registradas
          </Text>
        </ScrollView>
      </GradientView>
    );
  }

  // Determinar la propiedad seleccionada
  const selectedProperty =
    properties.length === 1
      ? properties[0]
      : properties.find((p) => p.id === selectedPropertyId) || properties[0];

  // Si hay múltiples propiedades pero no se ha seleccionado ninguna, usar la primera
  if (properties.length > 1 && !selectedPropertyId) {
    setSelectedPropertyId(properties[0].id);
  }

  return (
    <GradientView firstLineText="Mi propiedad">
      {/* Selector de propiedades para usuarios con múltiples propiedades */}
      <PropertySelector
        properties={properties}
        selectedPropertyId={selectedPropertyId}
        onPropertySelect={setSelectedPropertyId}
      />

      {/* Información básica de la propiedad */}
      <PropertyInfoSection property={selectedProperty} />

      {/* Reservaciones */}
      <ReservationsSection
        reservations={selectedProperty.reservations}
        property={selectedProperty}
      />

      <FinesSection
        fines={selectedProperty.fines}
        propertyId={selectedProperty.id}
      />

      <InfractionsSection
        infractions={selectedProperty.infractions}
        propertyId={selectedProperty.id}
      />

      {/* Quejas */}
      <ComplaintsSection
        complaints={selectedProperty.complaints}
        propertyId={selectedProperty.id}
      />

      {/* Reportes de mantenimiento */}
      {selectedProperty.maintenanceIssueReports.length > 0 && (
        <MaintenanceIssueReportsSection
          maintenanceIssueReports={selectedProperty.maintenanceIssueReports}
          property={selectedProperty}
        />
      )}
    </GradientView>
  );
};

const styles = StyleSheet.create({
  finesInfractionsContainer: {
    marginTop: theme.spacing.md,
  },
  noDataText: {
    textAlign: "center",
    fontSize: theme.fontSizes.lg,
    color: theme.colors.gray500,
    marginTop: theme.spacing.xl,
    padding: theme.spacing.lg,
  },
});
