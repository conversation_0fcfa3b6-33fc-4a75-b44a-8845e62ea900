import { useAuthContext } from "../../context/AuthContext";
import { theme } from "../../theme";
import { Entypo, MaterialCommunityIcons } from "@expo/vector-icons";
import { List } from "../../components/List/List";
import { ListItemProps } from "../../components/List/ListItem";
import { useMemo } from "react";
import { GradientView } from "../../components/layouts/GradientView";

export const AccountScreen: React.FC = () => {
  const { logout } = useAuthContext();

  const listItems: Omit<ListItemProps, "position">[] = useMemo(
    () => [
      {
        icon: (
          <MaterialCommunityIcons
            name="account-box-outline"
            size={theme.fontSizes.lg}
            color={theme.colors.black}
          />
        ),
        title: "Perfil",
        onPress: () => console.log("profile"),
        forwardIcon: true,
      },
      {
        icon: (
          <Entypo
            name="notification"
            size={theme.fontSizes.lg}
            color={theme.colors.black}
          />
        ),
        title: "Notificaciones",
        onPress: () => console.log("Some"),
        forwardIcon: true,
      },
      {
        icon: (
          <MaterialCommunityIcons
            name="file-refresh-outline"
            size={theme.fontSizes.lg}
            color={theme.colors.black}
          />
        ),
        title: "Solicitar cambio de datos",
        onPress: () => console.log("Some"),
        forwardIcon: true,
      },
      {
        icon: (
          <MaterialCommunityIcons
            name="file-document-outline"
            size={theme.fontSizes.lg}
            color={theme.colors.black}
          />
        ),
        title: "Terminos de servicios",
        onPress: () => console.log("Some"),
        forwardIcon: true,
      },
      {
        icon: (
          <MaterialCommunityIcons
            name="logout-variant"
            size={theme.fontSizes.lg}
            color={theme.colors.black}
          />
        ),
        title: "Cerrar sesión",
        onPress: logout,
      },
    ],
    [logout]
  );

  return (
    <GradientView firstLineText="Configuración">
      <List listItems={listItems} />
    </GradientView>
  );
};
