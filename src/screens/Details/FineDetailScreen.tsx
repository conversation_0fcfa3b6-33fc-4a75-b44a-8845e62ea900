import { StyleSheet, Text, View } from "react-native";
import { useRoute } from "@react-navigation/native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { GradientView } from "../../components/layouts/GradientView";
import { Card } from "../../components/main/Card";
import { Row } from "../../components/main/Row";
import { Col } from "../../components/main/Col";
import { theme } from "../../theme";
import { FineDetailRouteProp } from "../../navigation/types";
import { formatDateDMY } from "../../utils/date-time.utils";

const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat("es-MX", {
    style: "currency",
    currency: "MXN",
  }).format(amount);
};

export const FineDetailScreen: React.FC = () => {
  const route = useRoute<FineDetailRouteProp>();
  const { fine } = route.params;

  const statusColor = fine.isPaid ? theme.colors.success : theme.colors.error;
  const statusIcon = fine.isPaid ? "check-circle" : "alert-circle";
  const statusLabel = fine.isPaid ? "Pagada" : "Pendiente";

  return (
    <GradientView firstLineText="Detalle de Multa">
      {/* Header Card */}
      <Card style={styles.headerCard}>
        <Row align="center" style={styles.headerRow}>
          <View
            style={[
              styles.statusIndicator,
              { backgroundColor: `${statusColor}20` },
            ]}
          >
            <MaterialCommunityIcons
              name={statusIcon}
              size={32}
              color={statusColor}
            />
          </View>
          <Col style={styles.headerInfo}>
            <Text style={styles.fineAmount}>{formatCurrency(fine.amount)}</Text>
            <View style={[styles.statusChip, { backgroundColor: statusColor }]}>
              <Text style={styles.statusText}>{statusLabel}</Text>
            </View>
          </Col>
        </Row>
      </Card>

      {/* Fine Details */}
      <Card style={styles.detailCard}>
        <Text style={styles.sectionTitle}>Información de la Multa</Text>

        <View style={styles.detailRow}>
          <MaterialCommunityIcons
            name="cash"
            size={20}
            color={theme.colors.primary}
          />
          <View style={styles.detailContent}>
            <Text style={styles.detailLabel}>Monto</Text>
            <Text style={styles.detailValue}>
              {formatCurrency(fine.amount)}
            </Text>
          </View>
        </View>

        <View style={styles.detailRow}>
          <MaterialCommunityIcons
            name="text-box"
            size={20}
            color={theme.colors.primary}
          />
          <View style={styles.detailContent}>
            <Text style={styles.detailLabel}>Descripción</Text>
            <Text style={styles.detailValue}>{fine.description}</Text>
          </View>
        </View>

        <View style={styles.detailRow}>
          <MaterialCommunityIcons
            name="calendar"
            size={20}
            color={theme.colors.primary}
          />
          <View style={styles.detailContent}>
            <Text style={styles.detailLabel}>Fecha de emisión</Text>
            <Text style={styles.detailValue}>
              {formatDateDMY(fine.issuedAt)}
            </Text>
          </View>
        </View>

        <View style={styles.detailRow}>
          <MaterialCommunityIcons
            name="identifier"
            size={20}
            color={theme.colors.primary}
          />
          <View style={styles.detailContent}>
            <Text style={styles.detailLabel}>ID de Multa</Text>
            <Text style={styles.detailValue}>{fine.id}</Text>
          </View>
        </View>
      </Card>

      {/* Payment Status */}
      <Card style={styles.detailCard}>
        <Text style={styles.sectionTitle}>Estado de Pago</Text>

        <View style={styles.detailRow}>
          <MaterialCommunityIcons
            name={statusIcon}
            size={20}
            color={statusColor}
          />
          <View style={styles.detailContent}>
            <Text style={styles.detailLabel}>Estado</Text>
            <Text style={[styles.detailValue, { color: statusColor }]}>
              {statusLabel}
            </Text>
          </View>
        </View>

        {fine.paidAt && (
          <View style={styles.detailRow}>
            <MaterialCommunityIcons
              name="calendar-check"
              size={20}
              color={theme.colors.success}
            />
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Fecha de pago</Text>
              <Text style={styles.detailValue}>
                {formatDateDMY(fine.paidAt)}
              </Text>
            </View>
          </View>
        )}

        {!fine.isPaid && (
          <View style={styles.warningContainer}>
            <MaterialCommunityIcons
              name="alert"
              size={20}
              color={theme.colors.error}
            />
            <Text style={styles.warningText}>
              Esta multa está pendiente de pago. Contacte a la administración
              para más información.
            </Text>
          </View>
        )}
      </Card>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerCard: {
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.lg,
    flexDirection: "column",
  },
  headerRow: {
    marginBottom: 0,
  },
  statusIndicator: {
    width: 64,
    height: 64,
    borderRadius: theme.radii.lg,
    alignItems: "center",
    justifyContent: "center",
    marginRight: theme.spacing.md,
  },
  headerInfo: {
    flex: 1,
  },
  fineAmount: {
    fontSize: theme.fontSizes.xl,
    fontWeight: "700",
    color: theme.colors.black,
    marginBottom: theme.spacing.sm,
  },
  statusChip: {
    alignSelf: "flex-start",
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.radii.md,
  },
  statusText: {
    fontSize: theme.fontSizes.sm,
    fontWeight: "600",
    color: theme.colors.white,
  },
  detailCard: {
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.lg,
    flexDirection: "column",
  },
  sectionTitle: {
    fontSize: theme.fontSizes.lg,
    fontWeight: "700",
    color: theme.colors.black,
    marginBottom: theme.spacing.lg,
  },
  detailRow: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: theme.spacing.md,
  },
  detailContent: {
    flex: 1,
    marginLeft: theme.spacing.md,
  },
  detailLabel: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
    marginBottom: 2,
  },
  detailValue: {
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
    color: theme.colors.black,
  },
  warningContainer: {
    flexDirection: "row",
    alignItems: "flex-start",
    backgroundColor: `${theme.colors.error}10`,
    borderRadius: theme.radii.md,
    padding: theme.spacing.md,
    marginTop: theme.spacing.sm,
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.error,
  },
  warningText: {
    flex: 1,
    fontSize: theme.fontSizes.sm,
    color: theme.colors.error,
    marginLeft: theme.spacing.sm,
    lineHeight: 20,
  },
});
