import { StyleSheet } from "react-native";
import { WebView } from "react-native-webview";
import { Loading } from "../../components/Loading";
import { LinearGradient } from "expo-linear-gradient";
import { Col, Row, Title } from "../../components";
import { theme } from "../../theme";

export const PaymentsScreen: React.FC = () => {
  return (
    <LinearGradient
      colors={["#1B4959", "#DFD6C6"]}
      start={{ x: 0, y: 0 }}
      end={{ x: 2.5, y: 0 }}
      style={{ flex: 1 }}
    >
      <Row style={styles.header}>
        <Col>
          <Title size="xl" style={styles.headerText}>
            beResident
          </Title>
        </Col>
      </Row>
      <WebView
        source={{ uri: "https://app.beresident.mx" }}
        style={styles.webview}
        onLoadStart={() => console.log("loading...")}
        onLoadEnd={() => console.log("loaded")}
        onError={() => console.log("error")}
        onNavigationStateChange={(navState) => {
          console.log("navigation state changed", navState);
        }}
        renderLoading={() => <Loading />}
        startInLoadingState={true}
        javaScriptEnabled={true}
        domStorageEnabled={true}
        allowsBackForwardNavigationGestures={true}
        scalesPageToFit={true}
        bounces={false}
      />
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  header: {
    paddingHorizontal: 20,
  },
  headerText: {
    color: theme.colors.white,
  },
  webview: {
    flex: 1,
    paddingBottom: "10%",
  },
});
