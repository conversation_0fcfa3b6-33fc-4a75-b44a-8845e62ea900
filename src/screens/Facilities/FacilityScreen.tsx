import { Calendar, DateData } from "react-native-calendars";
import { View, Text, StyleSheet, ScrollView, Alert } from "react-native";
import { useState, useMemo } from "react";
import { theme } from "../../theme";
import {
  getDayName,
  getDayNumber,
  getStringTimeFromIso,
  getToday,
} from "../../utils/date-time.utils";
import { useNavigation, useRoute } from "@react-navigation/native";
import { Button, Col, Row } from "../../components";
import { GradientView } from "../../components/layouts/GradientView";
import { Ionicons } from "@expo/vector-icons";
import { AddReduce } from "../../components/main/AddReduce";
import { SimpleTimePicker } from "../../components/main/SimpleTimePicker";
import { useCachedQuery } from "../../hooks/useCachedQueries";
import { Me } from "../../interfaces/me";
import { QUERIES } from "../../constants/queries";
import { CreateReservation } from "../../interfaces/reservation";
import { useReservations } from "../../hooks/useReservations";
import { ReservationConfirmModal } from "../../components/modals/ReservationConfirmModal";
import { SuccessModal } from "../../components/modals/SuccessModal";
import { ErrorModal } from "../../components/modals/ErrorModal";
import { LoadingOverlay } from "../../components/LoadingOverlay";
import { DateTime } from "luxon";
import { FacilityRouteProp } from "../../navigation";

export const FacilityScreen: React.FC = () => {
  const navigation = useNavigation();
  const { params } = useRoute<FacilityRouteProp>();
  const me = useCachedQuery<Me>(QUERIES.ME);
  const propertyId = me.data?.properties[0].id ?? "";

  const today = getToday();
  const [selectedDate, setSelectedDate] = useState<string>(today);

  const [amountOfPeople, setAmountOfPeople] = useState<number>(1);
  const [selectedStartDateTime, setSelectedStartDateTime] =
    useState<Date | null>(null);
  const [selectedEndDateTime, setSelectedEndDateTime] = useState<Date | null>(
    null
  );

  // Estados para modales
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  // Hook para reservaciones
  const { createReservation } = useReservations();

  const facility = params.facility;

  const onSubmit = () => {
    // Validación completa
    if (!selectedStartDateTime || !selectedEndDateTime) {
      Alert.alert("Error", "Por favor selecciona un horario");
      return;
    }

    if (!propertyId) {
      Alert.alert("Error", "No se pudo obtener la información de la propiedad");
      return;
    }

    if (amountOfPeople < 1) {
      Alert.alert("Error", "Debe haber al menos 1 persona");
      return;
    }

    if (
      facility.maxAmountOfPeople &&
      amountOfPeople > facility.maxAmountOfPeople
    ) {
      Alert.alert(
        "Error",
        `Máximo ${facility.maxAmountOfPeople} personas permitidas`
      );
      return;
    }

    // Mostrar modal de confirmación
    setShowConfirmModal(true);
  };

  // Función para confirmar la reservación
  const handleConfirmReservation = async () => {
    if (!selectedStartDateTime || !selectedEndDateTime || !propertyId) return;

    try {
      // Convertir a UTC usando DateTime de Luxon
      const startDateTime = DateTime.fromJSDate(selectedStartDateTime).toUTC();
      const endDateTime = DateTime.fromJSDate(selectedEndDateTime).toUTC();

      const reservationData: CreateReservation = {
        propertyId,
        facilityId: facility.id,
        amountOfPeople,
        startDateTime: startDateTime.toISO()!,
        endDateTime: endDateTime.toISO()!,
      };

      await createReservation.mutateAsync(reservationData);

      // Cerrar modal de confirmación y mostrar éxito
      setShowConfirmModal(false);
      setShowSuccessModal(true);
    } catch (error: any) {
      // Cerrar modal de confirmación y mostrar error
      setShowConfirmModal(false);
      setErrorMessage(
        error?.response?.data?.message ?? "Error al crear la reservación"
      );
      setShowErrorModal(true);
    }
  };

  // Función para cerrar modal de éxito y navegar de vuelta
  const handleSuccessClose = () => {
    setShowSuccessModal(false);
    navigation.goBack();
  };

  // Función para cerrar modal de error
  const handleErrorClose = () => {
    setShowErrorModal(false);
  };

  const handleAmountOfPeopleChange = (newValue: number) => {
    const min = 1;
    const max = facility.maxAmountOfPeople ?? Infinity;
    const value = Math.max(min, Math.min(newValue, max));
    setAmountOfPeople(value);
  };

  const reservations = facility.reservations;

  const markedDates = useMemo(() => {
    const marks: Record<string, any> = {};

    reservations.forEach((r) => {
      const dateKey = r.startDateTime.split("T")[0];

      marks[dateKey] ??= { dots: [] };

      marks[dateKey].dots.push({
        color: theme.colors.primary,
      });
    });

    // Marca el día seleccionado
    if (selectedDate) {
      marks[selectedDate] = {
        ...(marks[selectedDate] ?? {}),
        selected: true,
        selectedColor: theme.colors.primaryDark,
        selectedTextColor: theme.colors.white,
        dots: marks[selectedDate]?.dots ?? [],
      };
    }

    return marks;
  }, [reservations, selectedDate]);

  const dailyReservations = useMemo(() => {
    return reservations
      .filter((r) => {
        const reservationDate =
          r.startDateTime.length > 10
            ? r.startDateTime.split("T")[0]
            : r.startDateTime;
        return reservationDate === selectedDate;
      })
      .sort((a, b) => {
        return a.startDateTime.localeCompare(b.startDateTime);
      });
  }, [reservations, selectedDate]);

  const handleDayPress = (day: DateData) => {
    setSelectedDate(day.dateString);
  };

  // Preparar datos para el modal de confirmación
  const reservationData = useMemo(() => {
    if (!selectedStartDateTime || !selectedEndDateTime) return null;

    const startDateTime = DateTime.fromJSDate(selectedStartDateTime).toUTC();
    const endDateTime = DateTime.fromJSDate(selectedEndDateTime).toUTC();

    return {
      facilityName: facility.name,
      amountOfPeople,
      startDateTime: startDateTime.toISO()!,
      endDateTime: endDateTime.toISO()!,
      selectedDate,
    };
  }, [
    selectedStartDateTime,
    selectedEndDateTime,
    amountOfPeople,
    facility.name,
    selectedDate,
  ]);

  return (
    <GradientView firstLineText={facility.name}>
      <ScrollView style={styles.container}>
        <Calendar
          onDayPress={handleDayPress}
          markedDates={markedDates}
          markingType="multi-dot"
          minDate={today}
          disableAllTouchEventsForDisabledDays
          theme={{
            todayTextColor: theme.colors.orange.dark,
            selectedDayBackgroundColor: theme.colors.primaryDark,
            arrowColor: theme.colors.primaryDark,
          }}
        />
        <ScrollView>
          {dailyReservations.length > 0 ? (
            dailyReservations.map((reservation, index) => (
              <View
                key={reservation.startDateTime + index}
                style={styles.reservationCard}
              >
                <Text style={styles.timeRange}>
                  {getStringTimeFromIso(reservation.startDateTime)} -{" "}
                  {getStringTimeFromIso(reservation.endDateTime)}
                </Text>
              </View>
            ))
          ) : (
            <Text style={styles.noReservations}>
              No hay reservas para este día.
            </Text>
          )}
        </ScrollView>
        <Row style={styles.reservationsContainer}>
          <View style={styles.dateInfo}>
            <Text style={styles.dayNumber}>{getDayNumber(selectedDate)}</Text>
            <Text style={styles.dayName}>{getDayName(selectedDate)}</Text>
            <Text style={styles.reservationsCount}>
              {dailyReservations.length}{" "}
              {dailyReservations.length === 1 ? "reserva" : "reservas"}
            </Text>
          </View>
          <Col style={styles.fieldsContainer}>
            <AddReduce
              value={amountOfPeople}
              onChange={handleAmountOfPeopleChange}
              icon={
                <Ionicons
                  name="people-outline"
                  size={theme.fontSizes.md}
                  color={theme.colors.gray700}
                />
              }
            />
            <SimpleTimePicker
              start={facility.open}
              end={facility.close}
              maxTimeOfStay={facility.maxTimeOfStay ?? Infinity}
              onChange={(value: {
                startTime: string | null;
                endTime: string | null;
              }) => {
                const startDateTime = new Date(
                  `${selectedDate}T${value.startTime}:00`
                );
                const endDateTime = new Date(
                  `${selectedDate}T${value.endTime}:00`
                );
                setSelectedStartDateTime(startDateTime);
                setSelectedEndDateTime(endDateTime);
              }}
            />
          </Col>
        </Row>

        <Button title="Reservar" onPress={onSubmit} />
      </ScrollView>

      {/* Loading Overlay */}
      <LoadingOverlay visible={createReservation.isPending} />

      {/* Modal de Confirmación */}
      <ReservationConfirmModal
        visible={showConfirmModal}
        reservationData={reservationData}
        onConfirm={handleConfirmReservation}
        onCancel={() => setShowConfirmModal(false)}
        isLoading={createReservation.isPending}
      />

      {/* Modal de Éxito */}
      <SuccessModal
        visible={showSuccessModal}
        title="¡Reservación Exitosa!"
        message="Tu reservación ha sido creada exitosamente. Recibirás una confirmación pronto."
        onClose={handleSuccessClose}
        buttonText="Continuar"
      />

      {/* Modal de Error */}
      <ErrorModal
        visible={showErrorModal}
        title="Error en la Reservación"
        message={errorMessage}
        onClose={handleErrorClose}
        buttonText="Intentar de nuevo"
      />
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: theme.spacing.md,
  },
  reservationsContainer: {
    marginTop: theme.spacing.lg,
    marginBottom: theme.spacing.lg,
  },
  title: {
    fontSize: theme.fontSizes.md,
    fontWeight: "bold",
    marginBottom: theme.spacing.md,
  },
  reservationCard: {
    backgroundColor: theme.colors.primaryLight,
    padding: theme.spacing.sm,
    borderRadius: theme.radii.md,
    marginBottom: theme.spacing.sm,
  },
  facilityName: {
    fontSize: theme.fontSizes.sm,
    fontWeight: "600",
    color: theme.colors.primaryDark,
  },
  timeRange: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray700,
    marginTop: theme.spacing.xs,
  },
  noReservations: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
    marginTop: theme.spacing.md,
  },
  dateInfo: {
    alignItems: "center",
    marginBottom: theme.spacing.md,
    marginRight: theme.spacing.md,
  },
  dayNumber: {
    fontSize: theme.fontSizes.xxxl,
    fontWeight: "bold",
    color: theme.colors.primaryDark,
  },
  dayName: {
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray700,
    textTransform: "capitalize",
    marginTop: theme.spacing.xs,
  },
  reservationsCount: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    marginTop: theme.spacing.xs,
  },
  fieldsContainer: {
    paddingHorizontal: theme.spacing.md,
  },
});
