import { useMutation, useQueryClient } from "@tanstack/react-query";
import { hoaClient } from "../api/axios-clients";
import { CreateReservation, Reservation } from "../interfaces/reservation";
import { AxiosError } from "axios";

export const useReservations = () => {
  const queryClient = useQueryClient();

  // Crear una nueva reservación
  const createReservation = useMutation({
    mutationFn: async (data: CreateReservation) => {
      const response = await hoaClient.post<Reservation>(
        "/mobile/reservation",
        data
      );
      return response.data;
    },
    onSuccess: () => {
      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: ["reservations"] });
      queryClient.invalidateQueries({ queryKey: ["me"] });
      queryClient.invalidateQueries({ queryKey: ["facilities"] });
    },
    onError: (error: AxiosError) => {
      console.error(error.response?.data);
    },
  });

  return {
    createReservation,
  };
};
