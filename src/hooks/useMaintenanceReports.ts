import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { hoaClient } from "../api/axios-clients";
import { CreateMaintenanceIssueReport, MaintenanceIssueReport } from "../interfaces/maintenance-issue-report";
import { Facility } from "../interfaces/facility";

export const useMaintenanceReports = () => {
  const queryClient = useQueryClient();

  // Obtener amenidades para el selector
  const facilities = useQuery({
    queryKey: ["facilities"],
    queryFn: async () => {
      const response = await hoaClient.get<Facility[]>("/mobile/facilities");
      return response.data;
    },
  });

  // Crear un nuevo reporte de mantenimiento
  const createMaintenanceReport = useMutation({
    mutationFn: async (data: CreateMaintenanceIssueReport) => {
      const response = await hoaClient.post<MaintenanceIssueReport>("/mobile/maintenance-issue-reports", data);
      return response.data;
    },
    onSuccess: () => {
      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: ["maintenance-issue-reports"] });
      queryClient.invalidateQueries({ queryKey: ["me"] });
    },
  });

  return {
    facilities,
    createMaintenanceReport,
  };
};
