import { useState, useCallback } from "react";

interface UseEntityModalResult<T> {
  isOpen: boolean;
  entity: T | null;
  openModal: (entity?: T) => void;
  closeModal: () => void;
  reset: () => void;
}

export const useEntityModal = <T>(): UseEntityModalResult<T> => {
  const [isOpen, setIsOpen] = useState(false);
  const [entity, setEntity] = useState<T | null>(null);

  const openModal = useCallback((entity?: T) => {
    setEntity(entity ?? null);
    setIsOpen(true);
  }, []);

  const closeModal = useCallback(() => {
    setIsOpen(false);
  }, []);

  const reset = useCallback(() => {
    setEntity(null);
    setIsOpen(false);
  }, []);

  return { isOpen, entity, openModal, closeModal, reset };
};
