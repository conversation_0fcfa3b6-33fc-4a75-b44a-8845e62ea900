import {
  useQuery,
  useQueryClient,
  UseQueryOptions,
} from "@tanstack/react-query";
import { hoaClient } from "../api/axios-clients";

export const useCachedQuery = <T>(
  endpoint: string,
  options?: Partial<UseQueryOptions<T>>
) => {
  const queryClient = useQueryClient();
  const cachedData = queryClient.getQueryData<T>([endpoint]);

  const query = useQuery<T>({
    queryKey: [endpoint],
    queryFn: async () => {
      const response = await hoaClient.get<T>(`/${endpoint}`);
      return response.data;
    },
    retry: 3,
    retryDelay: 3000,
    enabled: !cachedData,
    staleTime: Infinity,
    ...options,
  });

  console.log("Fetching endpoint:", endpoint);

  return {
    data: cachedData ?? query.data,
    isLoading: query.isLoading && !cachedData,
    isFetching: query.isFetching,
    refetch: query.refetch,
    error: query.error,
  };
};
