import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { hoaClient } from "../api/axios-clients";

export const useCrud = <T extends { id: string }>(endpoint: string) => {
  const queryClient = useQueryClient();

  // Obtener todos los registros
  const listQuery = useQuery({
    queryKey: [endpoint],
    queryFn: async () => {
      const response = await hoaClient.get<T[]>(`/${endpoint}`);
      return response.data;
    },
    staleTime: 1000 * 60 * 5, // Mantiene el caché fresco por 5 minutos
    refetchOnWindowFocus: false,
    initialData: undefined, // No usa datos en caché para la respuesta del hook
  });

  // Obtener un solo registro por ID
  const getByIdQuery = (id: string) =>
    useQuery({
      queryKey: [endpoint, id],
      queryFn: async () => {
        const response = await hoaClient.get<T>(`/${endpoint}/${id}`);
        return response.data;
      },
      enabled: !!id, // Solo ejecuta la query si hay un ID
    });

  // ✅ Función para invalidar y refrescar datos
  const refreshQueries = () => {
    queryClient.invalidateQueries({ queryKey: [endpoint] });
    queryClient.refetchQueries({ queryKey: [endpoint] });
  };

  // Crear un nuevo registro
  const createMutation = useMutation({
    mutationFn: async (data: Omit<T, "id">) => {
      const response = await hoaClient.post<T>(`/${endpoint}`, data);
      return response.data;
    },
    onSuccess: refreshQueries, // Invalida y refetch después de crear
  });

  // Actualizar un registro existente
  const updateMutation = useMutation({
    mutationFn: async (data: T) => {
      const response = await hoaClient.patch<T>(
        `/${endpoint}/${data.id}`,
        data
      );
      return response.data;
    },
    onSuccess: refreshQueries, // Invalida y refetch después de actualizar
  });

  // Eliminar un registro
  const deleteMutation = useMutation({
    mutationFn: async (id: string) => {
      await hoaClient.delete(`/${endpoint}/${id}`);
    },
    onSuccess: refreshQueries, // Invalida y refetch después de eliminar
  });

  return {
    listQuery,
    getByIdQuery,
    createMutation,
    updateMutation,
    deleteMutation,
  };
};
