import { useMutation, useQueryClient } from "@tanstack/react-query";
import { hoaClient } from "../api/axios-clients";
import { AuthRequest, AuthResponse } from "../types/auth";
import { config } from "../config/configuration";

export const useAuth = () => {
  const queryClient = useQueryClient();

  const { mutate, isPending, isSuccess, isError, error, data } = useMutation({
    mutationFn: async (authRequest: AuthRequest) => {
      const response = await hoaClient.post<AuthResponse>(
        `${config.baseUrl}/auth`,
        authRequest
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["auth"] });
      queryClient.refetchQueries({ queryKey: ["auth"] });
    },
  });

  return {
    login: mutate,
    isPending,
    isSuccess,
    isError,
    error,
    data,
  };
};
