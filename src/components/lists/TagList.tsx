import { View, Text, StyleSheet } from "react-native";
import { Tag } from "../../interfaces/tag";
import { theme } from "../../theme";

interface Props {
  tags: Tag[];
}

export const TagList: React.FC<Props> = ({ tags }) => {
  if (!tags.length) return <Text>Sin tags registrados</Text>;

  return (
    <View style={styles.section}>
      <Text style={styles.title}>Tags</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  section: {
    marginBottom: 24,
  },
  title: {
    fontSize: 18,
    fontWeight: "700",
    marginBottom: 12,
    color: theme.colors.primary,
  },
});
