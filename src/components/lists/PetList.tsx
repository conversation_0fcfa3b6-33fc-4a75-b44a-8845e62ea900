// src/components/property/PetList.tsx

import { Text, StyleSheet } from "react-native";
import { Pet } from "../../interfaces/pet";
import { Card } from "../main/Card";

interface Props {
  pets: Pet[];
}

export const PetList: React.FC<Props> = ({ pets }) => {
  if (!pets.length) {
    return <Text style={styles.message}>No hay mascotas registradas.</Text>;
  }

  return (
    <>
      <Text style={styles.sectionTitle}>🐶 Mascotas registradas</Text>
      {pets.map((pet) => (
        <Card key={pet.id} style={styles.card}>
          <Text style={styles.cardTitle}>{pet.name}</Text>
          <Text style={styles.cardDetail}>Tipo: {pet.type}</Text>
        </Card>
      ))}
    </>
  );
};

const styles = StyleSheet.create({
  message: {
    padding: 16,
    textAlign: "center",
    color: "#555",
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 12,
    marginTop: 12,
    paddingHorizontal: 16,
  },
  card: {
    marginHorizontal: 16,
    marginBottom: 12,
    padding: 16,
    backgroundColor: "#fff",
    borderRadius: 12,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  cardDetail: {
    fontSize: 14,
    color: "#444",
  },
});
