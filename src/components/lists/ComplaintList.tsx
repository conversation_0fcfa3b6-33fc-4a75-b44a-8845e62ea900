import { Text, StyleSheet } from "react-native";
import { Complaint } from "../../interfaces/complaint";
import { Card } from "../main/Card";

interface ComplaintListProps {
  complaints: Complaint[];
}

export const ComplaintList: React.FC<ComplaintListProps> = ({ complaints }) => {
  if (!complaints.length) {
    return <Text>No hay quejas registradas.</Text>;
  }
  return complaints.map((complaint) => (
    <Card style={styles.card} key={complaint.id}>
      <Text style={styles.title}>{complaint.detail}</Text>
      <Text style={styles.description}>{complaint.priority}</Text>
      <Text style={styles.status}>Estado: {complaint.status}</Text>
      <Text style={styles.description}>{complaint.createdAt}</Text>
      <Text style={styles.description}>{complaint.completedAt}</Text>
    </Card>
  ));
};

const styles = StyleSheet.create({
  card: {
    marginBottom: 16,
  },
  title: {
    fontWeight: "600",
    fontSize: 16,
    marginBottom: 4,
  },
  description: {
    color: "#444",
    marginBottom: 4,
  },
  status: {
    fontStyle: "italic",
    color: "#666",
  },
  date: {
    color: "#999",
    fontSize: 12,
  },
});
