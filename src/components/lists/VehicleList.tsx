// src/components/property/VehicleList.tsx

import { Text, StyleSheet } from "react-native";
import { Vehicle } from "../../interfaces/vehicle";
import { Card } from "../main/Card";
import { Col } from "../main/Col";

interface Props {
  vehicles: Vehicle[];
}

export const VehicleList: React.FC<Props> = ({ vehicles }) => {
  if (!vehicles.length) {
    return <Text style={styles.message}>No hay vehículos registrados.</Text>;
  }

  return (
    <>
      <Text style={styles.sectionTitle}>Vehículos:</Text>
      {vehicles.map((vehicle) => (
        <Card key={vehicle.id} style={styles.card}>
          <Col>
            <Text style={styles.cardTitle}>
              {vehicle.brand} {vehicle.model}
            </Text>
            <Text style={styles.cardDetail}>Color: {vehicle.color}</Text>
            <Text style={styles.cardDetail}>Placa: {vehicle.plate}</Text>
          </Col>
        </Card>
      ))}
    </>
  );
};

const styles = StyleSheet.create({
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginTop: 12,
    paddingHorizontal: 16,
  },
  message: {
    padding: 16,
    textAlign: "center",
    color: "#555",
  },

  card: {
    width: "40%",
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  cardDetail: {
    fontSize: 14,
    color: "#444",
  },
});
