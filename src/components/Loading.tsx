"use client";

import { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  Animated,
  StyleSheet,
  Easing,
  AccessibilityInfo,
} from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { theme } from "../theme";

export const Loading: React.FC = () => {
  const [loadingText, setLoadingText] = useState("Cargando datos");
  const [currentIcon, setCurrentIcon] = useState(0);

  // Referencias para las animaciones
  const bounceAnimationsRef = useRef(
    Array(5)
      .fill(null)
      .map(() => new Animated.Value(0))
  );
  const scaleAnimationsRef = useRef(
    Array(5)
      .fill(null)
      .map(() => new Animated.Value(1))
  );
  const rotateAnimationsRef = useRef(
    Array(5)
      .fill(null)
      .map(() => new Animated.Value(0))
  );
  const progressAnimation = useRef(new Animated.Value(0)).current;
  const textOpacityAnimation = useRef(new Animated.Value(1)).current;

  const bounceAnimations = bounceAnimationsRef.current;
  const scaleAnimations = scaleAnimationsRef.current;
  const rotateAnimations = rotateAnimationsRef.current;

  const funnyTexts = [
    "Despertando servidores",
    "Conectando con el servidor",
    "Solicitando datos",
    "Obteniendo datos",
    "Renderizando vistas",
    "Haciendo magia digital",
    "Preparando cosas geniales",
  ];

  const icons: React.ComponentProps<typeof MaterialCommunityIcons>["name"][] = [
    "lightning-bolt",
    "antenna",
    "assistant",
    "database-arrow-down",
    "view-carousel-outline",
    "auto-fix",
    "lightbulb-variant",
  ];

  // Iniciar animaciones de rebote
  useEffect(() => {
    bounceAnimations.forEach((animation, index) => {
      Animated.loop(
        Animated.sequence([
          Animated.delay(index * 200),
          Animated.parallel([
            Animated.timing(animation, {
              toValue: -20,
              duration: 500,
              useNativeDriver: true,
              easing: Easing.out(Easing.cubic),
            }),
            Animated.timing(scaleAnimations[index], {
              toValue: 1.1,
              duration: 500,
              useNativeDriver: true,
            }),
            Animated.timing(rotateAnimations[index], {
              toValue: index % 2 === 0 ? 0.1 : -0.1,
              duration: 500,
              useNativeDriver: true,
            }),
          ]),
          Animated.parallel([
            Animated.timing(animation, {
              toValue: 0,
              duration: 500,
              useNativeDriver: true,
              easing: Easing.in(Easing.cubic),
            }),
            Animated.timing(scaleAnimations[index], {
              toValue: 1,
              duration: 500,
              useNativeDriver: true,
            }),
            Animated.timing(rotateAnimations[index], {
              toValue: 0,
              duration: 500,
              useNativeDriver: true,
            }),
          ]),
        ])
      ).start();
    });

    // Animación de la barra de progreso
    Animated.loop(
      Animated.sequence([
        Animated.timing(progressAnimation, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: false,
          easing: Easing.inOut(Easing.ease),
        }),
        Animated.timing(progressAnimation, {
          toValue: 0,
          duration: 0,
          useNativeDriver: false,
        }),
      ])
    ).start();

    // Cambiar texto cada 2 segundos
    const textInterval = setInterval(() => {
      Animated.sequence([
        Animated.timing(textOpacityAnimation, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(textOpacityAnimation, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();

      setLoadingText((prev) => {
        const currentIndex = funnyTexts.indexOf(prev);
        const nextIndex = (currentIndex + 1) % funnyTexts.length;
        return funnyTexts[nextIndex];
      });
    }, 2000);

    // Cambiar icono cada segundo
    const iconInterval = setInterval(() => {
      setCurrentIcon((prev) => (prev + 1) % icons.length);
    }, 1000);

    // Anunciar para lectores de pantalla
    AccessibilityInfo.announceForAccessibility(
      "Cargando contenido, por favor espera un momento"
    );

    return () => {
      clearInterval(textInterval);
      clearInterval(iconInterval);
    };
  }, []);

  // Calcular rotación para animación
  const getRotateInterpolation = (animation: Animated.Value) => {
    return animation.interpolate({
      inputRange: [-0.1, 0, 0.1],
      outputRange: ["-10deg", "0deg", "10deg"],
    });
  };

  // Calcular ancho de la barra de progreso
  const progressWidth = progressAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: ["0%", "100%"],
  });

  return (
    <LinearGradient
      colors={["#1B4959", "#DFD6C6"]}
      start={{ x: 0, y: 0 }}
      end={{ x: 2.5, y: 0 }}
      style={{ flex: 1, justifyContent: "center" }}
    >
      <View style={styles.container}>
        <View style={styles.circlesContainer}>
          {[0, 1, 2, 3, 4].map((i) => (
            <Animated.View
              key={i}
              style={[
                styles.circle,
                {
                  transform: [
                    { translateY: bounceAnimations[i] },
                    { scale: scaleAnimations[i] },
                    { rotate: getRotateInterpolation(rotateAnimations[i]) },
                  ],
                },
              ]}
              accessible={false}
            >
              <MaterialCommunityIcons
                name={icons[(currentIcon + i) % icons.length]}
                size={24}
                color={theme.colors.primary}
              />
            </Animated.View>
          ))}
        </View>

        <View style={styles.textContainer}>
          <Animated.Text
            style={[styles.loadingText, { opacity: textOpacityAnimation }]}
            accessibilityRole="text"
          >
            {loadingText}
          </Animated.Text>
        </View>

        <View style={styles.progressContainer}>
          <Animated.View
            style={[styles.progressBar, { width: progressWidth }]}
          />
        </View>

        <Text style={styles.accessibilityText} accessibilityRole="alert">
          Cargando contenido, por favor espera un momento
        </Text>
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 32,
    borderRadius: 16,
    alignItems: "center",
    justifyContent: "center",
    minHeight: 200,
  },
  circlesContainer: {
    flexDirection: "row",
    gap: 16,
    marginBottom: 24,
  },
  circle: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: theme.colors.gold, // Púrpura
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  textContainer: {
    height: 32,
    overflow: "hidden",
    marginBottom: 8,
  },
  loadingText: {
    fontSize: 20,
    fontWeight: "bold",
    textAlign: "center",
    color: theme.colors.white, // Púrpura
  },
  progressContainer: {
    width: "100%",
    height: 8,
    backgroundColor: theme.colors.white, // Gris claro
    borderRadius: 4,
    marginTop: 16,
    overflow: "hidden",
  },
  progressBar: {
    height: "100%",
    backgroundColor: theme.colors.primary, // Púrpura
  },
  accessibilityText: {
    position: "absolute",
    height: 1,
    width: 1,
    overflow: "hidden",
    opacity: 0,
  },
});
