import { View, Text, StyleSheet } from "react-native";

interface SeverityCount {
  Grave?: number;
  Moderada?: number;
  Leve?: number;
}

interface PillProps {
  label: string;
  counts: SeverityCount;
  colors: {
    Grave: string;
    Moderada: string;
    Leve: string;
    background: string;
  };
}

export const SeverityPill: React.FC<PillProps> = ({
  label,
  counts,
  colors,
}) => {
  return (
    <View style={[styles.pill, { backgroundColor: colors.background }]}>
      <Text style={styles.label}>{label}</Text>
      {counts.Grave ? (
        <View style={[styles.circle, { backgroundColor: colors.Grave }]}>
          <Text style={styles.circleText}>{counts.Grave}</Text>
        </View>
      ) : null}
      {counts.Moderada ? (
        <View style={[styles.circle, { backgroundColor: colors.Moderada }]}>
          <Text style={styles.circleText}>{counts.Moderada}</Text>
        </View>
      ) : null}
      {counts.Leve ? (
        <View style={[styles.circle, { backgroundColor: colors.Leve }]}>
          <Text style={styles.circleText}>{counts.Leve}</Text>
        </View>
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  pill: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 20,
    paddingVertical: 6,
    paddingHorizontal: 12,
    marginBottom: 8,
    marginRight: 10,
    width: 190,
  },
  label: {
    color: "#111827",
    fontWeight: "600",
    marginRight: 8,
  },
  circle: {
    width: 20,
    height: 20,
    borderRadius: 10,
    marginLeft: 4,
    justifyContent: "center",
    alignItems: "center",
  },
  circleText: {
    fontSize: 10,
    color: "#FFFFFF",
    fontWeight: "bold",
  },
});
