import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  DimensionValue,
} from "react-native";
import Modal from "react-native-modal";
import { theme } from "../../theme";

interface InfoModalProps {
  visible: boolean;
  title?: string;
  onClose: () => void;
  children: React.ReactNode;
  padding?: DimensionValue;
}

export const InfoModal: React.FC<InfoModalProps> = ({
  visible,
  title,
  onClose,
  children,
  padding,
}) => {
  return (
    <Modal
      isVisible={visible}
      onBackdropPress={onClose}
      onBackButtonPress={onClose}
      style={styles.modal}
      swipeDirection="down"
      onSwipeComplete={onClose}
      propagateSwipe
      hideModalContentWhileAnimating
    >
      <View
        style={{
          ...styles.bottomSheet,
          padding: padding ?? styles.bottomSheet.padding,
        }}
      >
        {title && <Text style={styles.title}>{title}</Text>}
        <ScrollView style={styles.body}>{children}</ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modal: {
    justifyContent: "flex-end",
    margin: 0,
  },
  bottomSheet: {
    backgroundColor: theme.colors.white,
    borderTopLeftRadius: theme.radii.xl,
    borderTopRightRadius: theme.radii.xl,
    padding: theme.spacing.lg,
    height: "80%",
  },
  title: {
    fontSize: theme.fontSizes.xl,
    fontWeight: "800",
    marginBottom: theme.spacing.md,
    color: theme.colors.gold,
  },
  body: {
    flex: 1,
  },
});
