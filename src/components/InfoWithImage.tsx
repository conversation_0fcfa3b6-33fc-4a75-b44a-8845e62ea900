import { ImageBackground, StyleSheet, Text, View } from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { theme } from "../theme";
import { Pill } from "./Pill";
import { ScrollView } from "react-native-gesture-handler";

interface InfoWithImageProps {
  title: string;
  description: string;
  imageUrl: string;
  pillText?: string;
  onPress?: () => void;
  showReadMore?: boolean;
}

export const InfoWithImage: React.FC<InfoWithImageProps> = ({
  title,
  description,
  imageUrl,
  pillText,
  onPress,
  showReadMore = false,
}) => {
  return (
    <ScrollView>
      <View style={styles.imageContainer}>
        <ImageBackground
          source={{
            uri: imageUrl,
          }}
          style={styles.image}
        >
          {/* Overlay for better text readability */}
          <View style={styles.imageOverlay} />

          {/* Pill overlay on image */}

          {/* Read more indicator */}
          {showReadMore && onPress && (
            <View style={styles.readMoreIndicator}>
              <MaterialCommunityIcons
                name="arrow-expand"
                size={24}
                color={theme.colors.white}
              />
            </View>
          )}
        </ImageBackground>
      </View>

      <View style={styles.contentContainer}>
        {pillText && (
          <>
            <Text style={styles.date}>Fecha de creación:</Text>
            <Text style={styles.date}>{pillText}</Text>
          </>
        )}
        <View style={styles.textContainer}>
          <Text style={styles.title}>{title}</Text>
          <Text style={styles.description}>{description}</Text>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  textContainer: {
    marginTop: theme.spacing.sm,
  },
  imageContainer: {
    position: "relative",
  },
  image: {
    width: "100%",
    height: 240,
    justifyContent: "space-between",
  },
  imageOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "rgba(0, 0, 0, 0.05)",
    borderRadius: theme.radii.lg,
  },

  readMoreIndicator: {
    position: "absolute",
    top: theme.spacing.md,
    right: theme.spacing.md,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    borderRadius: theme.radii.xl,
    padding: theme.spacing.sm,
    zIndex: 2,
  },
  contentContainer: {
    paddingHorizontal: theme.spacing.xs,
    paddingVertical: theme.spacing.md,
  },
  title: {
    fontSize: theme.fontSizes.xl,
    fontWeight: "700",
    color: theme.colors.black,
    marginBottom: theme.spacing.sm,
    lineHeight: 28,
  },
  description: {
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray700,
    lineHeight: 22,
    marginBottom: theme.spacing.md,
  },
  readMoreContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-end",
    marginTop: theme.spacing.sm,
  },
  readMoreText: {
    fontSize: theme.fontSizes.sm,
    fontWeight: "600",
    color: theme.colors.primary,
    marginRight: 4,
  },
  date: {
    color: theme.colors.gold,
    fontSize: theme.fontSizes.xs,
    fontWeight: "600",
    marginLeft: 4,
  },
});
