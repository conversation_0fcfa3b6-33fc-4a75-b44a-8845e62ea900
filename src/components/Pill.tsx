import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { theme } from "../theme";

interface PillProps {
  label: string;
  onClick?: () => void;
}

export const Pill: React.FC<PillProps> = ({ label, onClick }) => {
  return (
    <>
      {onClick ? (
        <TouchableOpacity>
          <View style={styles.pill}>
            <Text style={styles.text}>{label}</Text>
          </View>
        </TouchableOpacity>
      ) : (
        <View style={styles.pill}>
          <Text style={styles.text}>{label}</Text>
        </View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  pill: {
    backgroundColor: theme.colors.primaryDark,
    borderRadius: theme.radii.pill,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    alignSelf: "flex-start", // Ajusta el ancho al contenido
  },
  text: {
    fontSize: theme.fontSizes.md,
    color: theme.colors.gold,
    fontWeight: "700",
  },
});
