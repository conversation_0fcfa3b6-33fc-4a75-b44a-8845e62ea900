import { View, Text, StyleSheet, TouchableOpacity } from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { theme } from "../theme";
import { formatCurrency } from "./Property/MonthlyChargesSection";

interface StatsProps {
  items: {
    icon: keyof typeof MaterialCommunityIcons.glyphMap;
    label: string;
    value: number;
    color: string;
    onPress?: () => void;
  }[];
  goTo?: {
    label: string;
    onPress: () => void;
  };
  tag?: {
    label: string;
    number: number;
    mainColor: string;
    secondaryColor: string;
  };
}

export const Stats: React.FC<StatsProps> = ({ items, goTo, tag }) => {
  return (
    <>
      <View style={styles.statsContainer}>
        {items.map((item) =>
          item.onPress ? (
            <TouchableOpacity
              key={item.label}
              style={styles.statItem}
              onPress={item.onPress}
              activeOpacity={0.7}
            >
              <MaterialCommunityIcons
                name={item.icon}
                size={theme.fontSizes.lg}
                color={item.color}
              />
              <Text style={styles.statNumber}>{item.value}</Text>
              <Text style={styles.statLabel}>{item.label}</Text>
            </TouchableOpacity>
          ) : (
            <View key={item.label} style={styles.statItem}>
              <MaterialCommunityIcons
                name={item.icon}
                size={theme.fontSizes.xl}
                color={item.color}
              />
              <Text style={styles.statNumberGeneral}>{item.value}</Text>
              <Text style={styles.statLabel}>{item.label}</Text>
            </View>
          )
        )}
      </View>
      {tag && tag.number > 0 && (
        <View
          style={[
            styles.totalOwedContainer,
            {
              backgroundColor: tag.secondaryColor,
              borderLeftColor: tag.mainColor,
            },
          ]}
        >
          <Text style={[styles.totalOwedLabel, { color: tag.mainColor }]}>
            {tag.label}:
          </Text>
          <Text style={[styles.totalOwedAmount, { color: tag.mainColor }]}>
            {formatCurrency(tag.number)}
          </Text>
        </View>
      )}
      {goTo && (
        <TouchableOpacity
          style={styles.statItem}
          onPress={goTo.onPress}
          activeOpacity={0.7}
        >
          <View style={styles.gotToContainer}>
            <Text style={styles.goToLabel}>{goTo.label}</Text>
            <MaterialCommunityIcons
              name="chevron-right"
              size={theme.fontSizes.md}
              color={theme.colors.primary}
            />
          </View>
        </TouchableOpacity>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    paddingTop: theme.spacing.sm,
  },
  statItem: {
    alignItems: "center",
    flex: 1,
  },
  statNumber: {
    fontSize: theme.fontSizes.lg,
    fontWeight: "700",
    color: theme.colors.primary,
    marginTop: theme.spacing.xs,
  },
  statLabel: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    textAlign: "center",
    marginTop: 2,
  },
  gotToContainer: {
    justifyContent: "center",
    marginTop: theme.spacing.md,
    paddingTop: theme.spacing.sm,
    width: "90%",
    borderTopWidth: 1,
    borderTopColor: theme.colors.gray200,
    flexDirection: "row",
    alignItems: "center",
  },
  goToLabel: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.primary,
    fontWeight: "600",
    marginRight: theme.spacing.xs,
  },
  statNumberGeneral: {
    fontSize: theme.fontSizes.xl,
    fontWeight: "700",
    color: theme.colors.black,
    marginTop: theme.spacing.xs,
  },
  totalOwedContainer: {
    borderRadius: theme.radii.md,
    padding: theme.spacing.md,
    marginTop: theme.spacing.md,
    borderLeftWidth: 4,
    alignItems: "center",
  },
  totalOwedLabel: {
    fontSize: theme.fontSizes.sm,
    fontWeight: "600",
  },
  totalOwedAmount: {
    fontSize: theme.fontSizes.xl,
    fontWeight: "700",
    marginTop: 2,
  },
});
