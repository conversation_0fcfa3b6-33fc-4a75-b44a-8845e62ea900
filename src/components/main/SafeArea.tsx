import { ReactNode } from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { theme } from "../../theme";

interface SafeAreaProps {
  children: ReactNode;
  padding?: boolean;
}

export const SafeArea: React.FC<SafeAreaProps> = ({ children, padding }) => {
  return (
    <SafeAreaView
      style={{
        padding: padding ? 20 : 0,
      }}
    >
      {children}
    </SafeAreaView>
  );
};
