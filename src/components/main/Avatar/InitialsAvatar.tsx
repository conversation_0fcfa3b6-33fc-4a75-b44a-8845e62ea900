import { View, Text, StyleSheet } from "react-native";
import { theme } from "../../../theme";

interface InitialsAvatarProps {
  name: string;
  size?: number;
}

export const InitialsAvatar: React.FC<InitialsAvatarProps> = ({
  name,
  size = theme.fontSizes.mj,
}) => {
  const initials = name
    .split(" ")
    .map((n) => n[0])
    .join("")
    .slice(0, 2)
    .toUpperCase();

  return (
    <View
      style={[
        styles.avatar,
        { width: size, height: size, borderRadius: size / 2 },
      ]}
    >
      <Text style={[styles.text, { fontSize: size / 2.5 }]}>{initials}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  avatar: {
    backgroundColor: theme.colors.primary, // tu color primary
    alignItems: "center",
    justifyContent: "center",
  },
  text: {
    color: "white",
    fontWeight: "bold",
  },
});
