import { Image, StyleSheet } from "react-native";

interface AvatarProps {
  uri: string;
  size?: number;
}

export const Avatar: React.FC<AvatarProps> = ({ uri, size = 48 }) => {
  return (
    <Image
      source={{ uri }}
      style={[
        styles.avatar,
        { width: size, height: size, borderRadius: size / 2 },
      ]}
    />
  );
};

const styles = StyleSheet.create({
  avatar: {
    borderWidth: 1,
    borderColor: "#D1D5DB", // gray-300
  },
});
