import { theme } from "../../../theme";
import { Avatar } from "./Avatar";
import { InitialsAvatar } from "./InitialsAvatar";

interface SmartAvatarProps {
  name: string;
  imageUri?: string;
  size?: number;
}

export const SmartAvatar: React.FC<SmartAvatarProps> = ({
  name,
  imageUri,
  size = theme.fontSizes.mj,
}) => {
  if (imageUri) {
    return <Avatar uri={imageUri} size={size} />;
  }
  return <InitialsAvatar name={name} size={size} />;
};
