import { ReactNode } from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { Title } from "./Title";
import { ScrollView } from "react-native-gesture-handler";
import { theme } from "../../theme";
import { StyleSheet, View } from "react-native";

interface ScreenBaseProps {
  children: ReactNode;
  title?: string;
}

export const ScreenBase: React.FC<ScreenBaseProps> = ({ children, title }) => {
  return (
    <SafeAreaView style={styles.safeArea}>
      <ScrollView
        contentContainerStyle={styles.scrollViewContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.view}>
          {title && <Title size="l">{title}</Title>}
          {children}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    padding: 20,
    flex: 1,
  },
  view: {
    flexDirection: "column",
  },
  scrollViewContent: {
    flexGrow: 1,
    paddingBottom: theme.spacing.xl,
  },
});
