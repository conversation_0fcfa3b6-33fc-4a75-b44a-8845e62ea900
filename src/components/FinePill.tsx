import { View, Text, StyleSheet } from "react-native";
import { theme } from "../theme";

interface FinesInfracrionsPillProps {
  total: number;
  color: string;
  text: string;
}

export const FinesInfractionsPill: React.FC<FinesInfracrionsPillProps> = ({
  total,
  color,
  text,
}) => {
  return (
    <View style={{ ...styles.pill, backgroundColor: color }}>
      <Text style={styles.label}>{text}</Text>
      <View style={styles.circle}>
        <Text style={{ ...styles.circleText, color: color }}>{total}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  pill: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 20,
    paddingVertical: 6,
    paddingHorizontal: 12,
    marginRight: 10,

    shadowColor: theme.colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: theme.radii.sm,
    elevation: 3, // para Android
  },
  label: {
    color: "#FFFFFF",
    fontWeight: "700",
    marginRight: 8,
  },
  circle: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: "#FFFFFF",
    justifyContent: "center",
    alignItems: "center",
  },
  circleText: {
    fontWeight: "bold",
  },
});
