import { StyleSheet, Text, View } from "react-native";
import { theme } from "../../theme";

interface NoDataSectionProps {
  sectionTitle: string;
}

export const NoDataSection: React.FC<NoDataSectionProps> = ({
  sectionTitle,
}) => {
  return (
    <View style={styles.container}>
      <Text>Sin {sectionTitle} registradas</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: theme.spacing.sm,
  },
});
