import { ParkingSpot } from "../../interfaces/parking-spot";
import { theme } from "../../theme";
import { Card, Col, Row } from "../main";
import { Section } from "../main/Section";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";

interface ParkingSpotsSectionProps {
  parkingSpots: ParkingSpot[];
}

const getStatusColor = (isAvailable: boolean): string => {
  return isAvailable ? theme.colors.success : theme.colors.error;
};

const getStatusIcon = (
  isAvailable: boolean
): keyof typeof MaterialCommunityIcons.glyphMap => {
  return isAvailable ? "check-circle" : "close-circle";
};

const getStatusText = (isAvailable: boolean): string => {
  return isAvailable ? "Disponible" : "Ocupado";
};

export const ParkingSpotsSection: React.FC<ParkingSpotsSectionProps> = ({
  parkingSpots,
}) => {
  if (!parkingSpots.length) {
    return (
      <Section title="Lugares de estacionamiento">
        <Text style={styles.noDataText}>
          No hay lugares de estacionamiento asignados
        </Text>
      </Section>
    );
  }

  // Separar por disponibilidad
  const availableSpots = parkingSpots.filter((spot) => spot.isAvailable);
  const occupiedSpots = parkingSpots.filter((spot) => !spot.isAvailable);

  return (
    <Section title="Lugares de estacionamiento">
      <Col>
        {/* Resumen de disponibilidad */}
        <Card style={styles.summaryCard}>
          <Row align="center" style={styles.summaryRow}>
            <View style={styles.summaryItem}>
              <MaterialCommunityIcons
                name="parking"
                size={24}
                color={theme.colors.primary}
              />
              <Text style={styles.summaryNumber}>{parkingSpots.length}</Text>
              <Text style={styles.summaryLabel}>Total</Text>
            </View>
            <View style={styles.summaryItem}>
              <MaterialCommunityIcons
                name="check-circle"
                size={24}
                color={theme.colors.success}
              />
              <Text style={styles.summaryNumber}>{availableSpots.length}</Text>
              <Text style={styles.summaryLabel}>Disponibles</Text>
            </View>
            <View style={styles.summaryItem}>
              <MaterialCommunityIcons
                name="close-circle"
                size={24}
                color={theme.colors.error}
              />
              <Text style={styles.summaryNumber}>{occupiedSpots.length}</Text>
              <Text style={styles.summaryLabel}>Ocupados</Text>
            </View>
          </Row>
        </Card>

        {/* Lista de lugares */}
        <Row style={styles.spotsGrid}>
          {parkingSpots.map((parkingSpot) => (
            <TouchableOpacity
              key={parkingSpot.id}
              style={[
                styles.spotCard,
                {
                  backgroundColor: parkingSpot.isAvailable
                    ? `${theme.colors.success}15`
                    : `${theme.colors.error}15`,
                  borderColor: getStatusColor(parkingSpot.isAvailable),
                },
              ]}
              activeOpacity={0.7}
            >
              <MaterialCommunityIcons
                name={getStatusIcon(parkingSpot.isAvailable)}
                size={20}
                color={getStatusColor(parkingSpot.isAvailable)}
              />
              <Text style={styles.spotNumber}>{parkingSpot.spotNumber}</Text>
              <Text
                style={[
                  styles.spotStatus,
                  { color: getStatusColor(parkingSpot.isAvailable) },
                ]}
              >
                {getStatusText(parkingSpot.isAvailable)}
              </Text>
            </TouchableOpacity>
          ))}
        </Row>
      </Col>
    </Section>
  );
};

const styles = StyleSheet.create({
  summaryCard: {
    flexDirection: "row",
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.md,
  },
  summaryRow: {
    justifyContent: "space-around",
    width: "100%",
  },
  summaryItem: {
    alignItems: "center",
    flex: 1,
  },
  summaryNumber: {
    fontSize: theme.fontSizes.xl,
    fontWeight: "700",
    color: theme.colors.black,
    marginTop: theme.spacing.xs,
  },
  summaryLabel: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    textAlign: "center",
    marginTop: 2,
  },
  spotsGrid: {
    flexWrap: "wrap",
    justifyContent: "space-between",
    gap: theme.spacing.sm,
  },
  spotCard: {
    width: "30%",
    minWidth: 100,
    aspectRatio: 1,
    borderRadius: theme.radii.lg,
    borderWidth: 2,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: theme.spacing.sm,
    padding: theme.spacing.sm,
  },
  spotNumber: {
    fontSize: theme.fontSizes.lg,
    fontWeight: "700",
    color: theme.colors.black,
    marginTop: theme.spacing.xs,
  },
  spotStatus: {
    fontSize: theme.fontSizes.xs,
    fontWeight: "600",
    textAlign: "center",
    marginTop: 2,
    textTransform: "uppercase",
  },
  noDataText: {
    textAlign: "center",
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    padding: theme.spacing.lg,
    fontStyle: "italic",
  },
});
