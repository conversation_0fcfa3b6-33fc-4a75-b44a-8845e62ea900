import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { useNavigation } from "@react-navigation/native";
import { Complaint } from "../../interfaces/complaint";
import { Card, Section } from "../main";
import { theme } from "../../theme";
import { PROPERTY_SCREENS } from "../../navigation/constants";
import { PropertyStackNavigationProp } from "../../navigation/types";
import { Property } from "../../interfaces/property";
import { formatDateDMY } from "../../utils/date-time.utils";
import {
  getPriorityColor,
  getPriorityLabel,
  getStatusColor,
  getStatusIcon,
  getStatusLabel,
} from "../../utils/convertions";
import { Row } from "../main/Row";
import { Col } from "../main/Col";
import { MaterialCommunityIcons } from "@expo/vector-icons";

interface ComplaintsSectionProps {
  complaints: Complaint[];
  propertyId: Property["id"];
}

export const ComplaintsSection: React.FC<ComplaintsSectionProps> = ({
  complaints,
  propertyId,
}) => {
  const navigation = useNavigation<PropertyStackNavigationProp>();

  if (!complaints.length) {
    return (
      <Section title="Quejas">
        <Text style={styles.noDataText}>No hay quejas registradas</Text>
      </Section>
    );
  }

  return (
    <Section title="Quejas">
      <>
        {complaints.map((complaint) => (
          <TouchableOpacity
            key={complaint.id}
            onPress={() =>
              navigation.navigate(PROPERTY_SCREENS.COMPLAINT_DETAIL, {
                complaint,
              })
            }
            activeOpacity={0.7}
          >
            <Card
              style={[
                styles.complaintCard,
                { borderLeftColor: getPriorityColor(complaint.priority) },
              ]}
            >
              <Row align="flex-start">
                <Col style={styles.complaintInfo}>
                  <Row align="center" style={styles.headerRow}>
                    <View
                      style={[
                        styles.priorityChip,
                        {
                          backgroundColor: getPriorityColor(complaint.priority),
                        },
                      ]}
                    >
                      <Text style={styles.priorityText}>
                        {getPriorityLabel(complaint.priority)}
                      </Text>
                    </View>
                    <View
                      style={[
                        styles.statusChip,
                        { backgroundColor: getStatusColor(complaint.status) },
                      ]}
                    >
                      <MaterialCommunityIcons
                        name={getStatusIcon(complaint.status)}
                        size={12}
                        color={theme.colors.white}
                      />
                      <Text style={styles.statusText}>
                        {getStatusLabel(complaint.status)}
                      </Text>
                    </View>
                  </Row>
                  <Text style={styles.complaintDetail} numberOfLines={3}>
                    {complaint.detail}
                  </Text>
                  <Row align="center" style={styles.dateRow}>
                    <MaterialCommunityIcons
                      name="calendar"
                      size={14}
                      color={theme.colors.gray500}
                    />
                    <Text style={styles.dateText}>
                      Creada: {formatDateDMY(complaint.createdAt)}
                    </Text>
                    {!!complaint.completedAt && (
                      <>
                        <MaterialCommunityIcons
                          name="check"
                          size={14}
                          color={theme.colors.success}
                          style={styles.completedIcon}
                        />
                        <Text style={styles.completedText}>
                          Completada: {formatDateDMY(complaint.completedAt)}
                        </Text>
                      </>
                    )}
                  </Row>
                </Col>
                <MaterialCommunityIcons
                  name="chevron-right"
                  size={theme.fontSizes.lg}
                  color={theme.colors.gray500}
                />
              </Row>
            </Card>
          </TouchableOpacity>
        ))}
      </>
    </Section>
  );
};

const styles = StyleSheet.create({
  summaryCard: {
    flexDirection: "column",
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.md,
  },
  noDataText: {
    textAlign: "center",
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    padding: theme.spacing.lg,
    fontStyle: "italic",
  },
  complaintCard: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: theme.spacing.md,
    padding: theme.spacing.md,
    borderRadius: theme.radii.md,
    borderLeftWidth: 4,
  },
  priorityIndicator: {
    width: 48,
    height: 48,
    borderRadius: theme.radii.lg,
    alignItems: "center",
    justifyContent: "center",
    marginRight: theme.spacing.md,
  },
  complaintInfo: {
    flex: 1,
    paddingLeft: theme.spacing.md,
  },
  headerRow: {
    justifyContent: "space-between",
    marginBottom: theme.spacing.sm,
  },
  priorityChip: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.radii.sm,
    marginRight: theme.spacing.sm,
  },
  priorityText: {
    fontSize: theme.fontSizes.xs,
    fontWeight: "600",
    color: theme.colors.white,
    textTransform: "uppercase",
  },
  statusChip: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.radii.sm,
  },
  statusText: {
    fontSize: theme.fontSizes.xs,
    fontWeight: "600",
    color: theme.colors.white,
    marginLeft: 4,
    textTransform: "capitalize",
  },
  complaintDetail: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.black,
    lineHeight: 18,
    marginBottom: theme.spacing.sm,
  },
  dateRow: {
    marginBottom: theme.spacing.xs,
  },
  dateText: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    marginLeft: 6,
  },
  completedIcon: {
    marginLeft: theme.spacing.md,
  },
  completedText: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.success,
    marginLeft: 6,
  },
});
