import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { Vehicle } from "../../interfaces/vehicle";
import { Col, Row, Section } from "../main";
import { Card } from "../main/Card";
import { theme } from "../../theme/theme";

interface VehiclesSectionProps {
  vehicles: Vehicle[];
}

export const VehiclesSection: React.FC<VehiclesSectionProps> = ({
  vehicles,
}) => {
  if (!vehicles.length) {
    return (
      <Section title="Vehículos">
        <Text style={styles.noDataText}>No hay vehículos registrados</Text>
      </Section>
    );
  }

  return (
    <Section title="Vehículos">
      <Col>
        {vehicles.map((vehicle) => (
          <TouchableOpacity key={vehicle.id} activeOpacity={0.7}>
            <Card style={styles.vehicleCard}>
              <Row align="center">
                <View style={styles.iconContainer}>
                  <MaterialCommunityIcons
                    name="car"
                    size={24}
                    color={theme.colors.primary}
                  />
                </View>
                <Col style={styles.vehicleInfo}>
                  <Text style={styles.vehicleBrandModel}>
                    {vehicle.brand} {vehicle.model}
                  </Text>
                  <Row align="center" style={styles.detailsRow}>
                    <MaterialCommunityIcons
                      name="palette"
                      size={14}
                      color={theme.colors.gray500}
                    />
                    <Text style={styles.vehicleColor}>{vehicle.color}</Text>
                  </Row>
                </Col>
                <View style={styles.plateContainer}>
                  <Text style={styles.plateText}>{vehicle.plate}</Text>
                </View>
              </Row>
            </Card>
          </TouchableOpacity>
        ))}
      </Col>
    </Section>
  );
};

const styles = StyleSheet.create({
  vehicleCard: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: theme.spacing.sm,
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.md,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: theme.radii.lg,
    alignItems: "center",
    justifyContent: "center",
    marginRight: theme.spacing.md,
    backgroundColor: `${theme.colors.primary}20`,
  },
  vehicleInfo: {
    flex: 1,
  },
  vehicleBrandModel: {
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
    color: theme.colors.black,
    marginBottom: 4,
  },
  detailsRow: {
    marginTop: 2,
  },
  vehicleColor: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
    marginLeft: 4,
    textTransform: "capitalize",
  },

  plateContainer: {
    backgroundColor: theme.colors.primaryDark,
    borderRadius: theme.radii.md,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderWidth: 2,
    borderColor: theme.colors.primary,
  },
  plateText: {
    fontSize: theme.fontSizes.sm,
    fontWeight: "700",
    color: theme.colors.white,
    letterSpacing: 1,
  },
  noDataText: {
    textAlign: "center",
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    padding: theme.spacing.lg,
    fontStyle: "italic",
  },
});
