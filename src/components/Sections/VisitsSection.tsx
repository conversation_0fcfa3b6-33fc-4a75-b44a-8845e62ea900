import { View, Text, StyleSheet } from "react-native";
import { Card } from "../main/Card";
import { Visit } from "../../interfaces/visit";
import { Col } from "../main/Col";
import { theme } from "../../theme";

interface VisitsSectionProps {
  visits: Visit[];
}

export const VisitsSection: React.FC<VisitsSectionProps> = ({ visits }) => {
  if (!visits.length) return null;

  return (
    <View style={styles.container}>
      <Text>Visitas recientes</Text>
      {visits.map((visit) => (
        <Card key={visit.id}>
          <Col>
            <Text style={styles.label}>
              Nombre: <Text style={styles.value}>{visit.visitorName}</Text>
            </Text>
            <Text style={styles.label}>
              Método: <Text style={styles.value}>{visit.visitMethod}</Text>
            </Text>
            {visit.vehiclePlate && (
              <Text style={styles.label}>
                Placa: <Text style={styles.value}>{visit.vehiclePlate}</Text>
              </Text>
            )}
            <Text style={styles.label}>
              Fecha: <Text style={styles.value}>{visit.checkInTime}</Text>
            </Text>
          </Col>
        </Card>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: theme.spacing.lg,
  },
  label: {
    fontWeight: "600",
    marginBottom: 2,
  },
  value: {
    fontWeight: "400",
    color: "#333",
  },
});
