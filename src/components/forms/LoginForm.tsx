import { z } from "zod";
import { View, StyleSheet, Text, KeyboardAvoidingView } from "react-native";
import { useZodForm } from "../../hooks/useZodForm";
import { <PERSON><PERSON><PERSON>, Feather } from "@expo/vector-icons";
import { FormField } from "./FormField";
import { useAuth } from "../../hooks/useAuth";
import { useAuthContext } from "../../context/AuthContext";
import { Button } from "../main/buttons/Button";
import { theme } from "../../theme";
import { LoginFormValues, loginSchema } from "../../schemas/schemas";

export const LoginForm: React.FC = () => {
  const { login: loginMutation, isError, error } = useAuth();
  const { control, handleSubmit } = useZodForm(loginSchema);
  const { checkSession } = useAuthContext();

  const onSubmit = (data: LoginFormValues) => {
    loginMutation(data, {
      onSuccess: () => checkSession(),
    });
  };

  return (
    <View style={styles.container}>
      {isError && (
        <Text style={{ color: theme.colors.error }}>{error?.message}</Text>
      )}
      <FormField
        control={control}
        name="email"
        placeholder="<EMAIL>"
        keyboardType="email-address"
        icon={
          <Ionicons
            name="mail-outline"
            size={theme.fontSizes.md}
            color={theme.colors.gray700}
          />
        }
      />

      <FormField
        control={control}
        name="password"
        placeholder="••••••••"
        secureTextEntry
        icon={
          <Feather
            name="lock"
            size={theme.fontSizes.md}
            color={theme.colors.gray700}
          />
        }
      />
      <KeyboardAvoidingView>
        <Button onPress={handleSubmit(onSubmit)} title="Ingresar" />
      </KeyboardAvoidingView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: "100%",
  },
});
