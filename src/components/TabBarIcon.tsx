import { MaterialCommunityIcons } from "@expo/vector-icons";
import { TAB_ICONS } from "../navigation/constants";
import { StyleSheet, View } from "react-native";
import { theme } from "../theme";

export interface TabBarIconProps {
  color: string;
  size: number;
  routeName: string;
  focused: boolean;
}

export const TabBarIcon: React.FC<TabBarIconProps> = ({
  color,
  size,
  routeName,
  focused,
}) => {
  // Get icon name from constants, fallback to person icon
  const iconName = TAB_ICONS[routeName as keyof typeof TAB_ICONS] || "person";

  return (
    <View style={focused ? styles.focusedIconContainer : styles.iconContainer}>
      <MaterialCommunityIcons
        name={iconName as keyof typeof MaterialCommunityIcons.glyphMap}
        size={focused ? size + 4 : size + 2}
        color={color}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  iconContainer: {
    justifyContent: "center",
    alignItems: "center",
  },
  focusedIconContainer: {
    height: 60,
    width: 60,
    backgroundColor: theme.colors.gold,
    borderRadius: theme.radii.xl,
    padding: theme.spacing.xs,
    justifyContent: "center",
    alignContent: "center",
    alignItems: "center",
  },
});
