import { Entypo } from "@expo/vector-icons";
import { StyleSheet, Text, TouchableOpacity, ViewStyle } from "react-native";
import { Row } from "../main/Row";
import { theme } from "../../theme";

export type Position = "top" | "middle" | "bottom" | "single";

export interface ListItemProps {
  icon: React.ReactElement;
  title: string;
  position: Position;
  onPress: () => void;
  forwardIcon?: boolean;
}

export const ListItem: React.FC<ListItemProps> = ({
  icon,
  title,
  position,
  onPress,
  forwardIcon,
}) => {
  return (
    <TouchableOpacity
      onPress={onPress}
      style={[styles.row, positionStyles[position]]}
    >
      <Row style={styles.content} justify="space-between">
        <Row align="center">
          {icon}
          <Text style={styles.text}>{title}</Text>
        </Row>
        {forwardIcon && (
          <Entypo
            name="chevron-small-right"
            size={theme.fontSizes.lg}
            color="black"
          />
        )}
      </Row>
    </TouchableOpacity>
  );
};

const baseBorderRadius = theme.radii.md;

const positionStyles: Record<Position, ViewStyle> = {
  top: {
    borderTopLeftRadius: baseBorderRadius,
    borderTopRightRadius: baseBorderRadius,
  },
  middle: {},
  bottom: {
    borderBottomLeftRadius: baseBorderRadius,
    borderBottomRightRadius: baseBorderRadius,
  },
  single: {
    borderRadius: baseBorderRadius,
  },
};

const styles = StyleSheet.create({
  row: {
    backgroundColor: theme.colors.white,
    overflow: "hidden",
  },
  content: {
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.md,
  },
  text: {
    fontSize: theme.fontSizes.md,
    marginLeft: theme.spacing.md,
  },
});
