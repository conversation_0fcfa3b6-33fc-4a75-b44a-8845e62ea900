import { ListItem, ListItemProps } from "./ListItem";

interface ListProps {
  listItems: Omit<ListItemProps, "position">[];
}
const getPosition = ({ index, size }: { index: number; size: number }) => {
  if (size === 1) return "single";
  if (index === 0) return "top";
  if (index === size - 1) return "bottom";
  return "middle";
};

export const List: React.FC<ListProps> = ({ listItems }) => {
  const size = listItems.length;

  return (
    <>
      {listItems.map((listItem, index) => (
        <ListItem
          key={`${index + 1}-list`}
          position={getPosition({ index, size })}
          icon={listItem.icon}
          title={listItem.title}
          onPress={listItem.onPress}
          forwardIcon={listItem.forwardIcon}
        />
      ))}
    </>
  );
};
