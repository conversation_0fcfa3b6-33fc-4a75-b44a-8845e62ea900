import { Text, StyleSheet, View } from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import { theme } from "../../theme";
import { Card } from "../main/Card";
import { Row } from "../main/Row";
import { Col } from "../main/Col";
import { Section } from "../main/Section";
import { Stats } from "../Stats";
import { getPropertyIcon, getPropertyTypeLabel } from "../../utils/convertions";
import { PROPERTY_SCREENS } from "../../navigation/constants";
import { PropertyStackNavigationProp } from "../../navigation/types";
import { PartialProperty } from "../../interfaces/property";

interface PropertyInfoSectionProps {
  property: PartialProperty;
}

export const PropertyInfoSection: React.FC<PropertyInfoSectionProps> = ({
  property,
}) => {
  const navigation = useNavigation<PropertyStackNavigationProp>();

  return (
    <Section>
      <Card style={styles.card}>
        <Col>
          {/* Tipo de propiedad */}
          <Row align="center" style={styles.infoRow}>
            <MaterialCommunityIcons
              name={getPropertyIcon(property.type)}
              size={theme.fontSizes.lg}
              color={theme.colors.primary}
            />
            <Col style={styles.textContainer}>
              <Text style={styles.label}>Tipo</Text>
              <Text style={styles.value}>
                {getPropertyTypeLabel(property.type)}
              </Text>
            </Col>
          </Row>

          {/* Dirección */}
          <Row align="center" style={styles.infoRow}>
            <MaterialCommunityIcons
              name="map-marker"
              size={theme.fontSizes.lg}
              color={theme.colors.primary}
            />
            <Col style={styles.textContainer}>
              <Text style={styles.label}>Dirección</Text>
              <Text style={styles.value}>{property.address}</Text>
            </Col>
          </Row>

          {/* Estadísticas rápidas */}
          <View style={styles.statsContainer}>
            <Stats
              items={[
                {
                  icon: "account-group",
                  label: "Residentes",
                  value: property._count.residents || 0,
                  color: theme.colors.primary,
                  onPress: () => {
                    navigation.navigate(PROPERTY_SCREENS.PROPERTY_RESIDENTS, {
                      propertyId: property.id,
                    });
                  },
                },
                {
                  icon: "car",
                  label: "Vehículos",
                  value: property._count.vehicles || 0,
                  color: theme.colors.primary,
                  onPress: () => {
                    navigation.navigate(PROPERTY_SCREENS.PROPERTY_VEHICLES, {
                      propertyId: property.id,
                    });
                  },
                },
                {
                  icon: "paw",
                  label: "Mascotas",
                  value: property._count.pets || 0,
                  color: theme.colors.primary,
                  onPress: () => {
                    navigation.navigate(PROPERTY_SCREENS.PROPERTY_PETS, {
                      propertyId: property.id,
                    });
                  },
                },
                {
                  icon: "parking",
                  label: "Estacionamientos",
                  value: property._count.parkingSpots || 0,
                  color: theme.colors.primary,
                  onPress: () => {
                    navigation.navigate(
                      PROPERTY_SCREENS.PROPERTY_PARKING_SPOTS,
                      {
                        propertyId: property.id,
                      }
                    );
                  },
                },
              ]}
            />
          </View>
        </Col>
      </Card>
    </Section>
  );
};

const styles = StyleSheet.create({
  card: {
    flexDirection: "column",
  },
  infoRow: {
    marginBottom: theme.spacing.md,
  },
  textContainer: {
    marginLeft: theme.spacing.md,
    flex: 1,
  },
  label: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
    fontWeight: "500",
  },
  value: {
    fontSize: theme.fontSizes.md,
    color: theme.colors.black,
    fontWeight: "600",
    marginTop: 2,
  },
  statsContainer: {
    borderTopWidth: 1,
    borderTopColor: theme.colors.gray200,
  },
});
