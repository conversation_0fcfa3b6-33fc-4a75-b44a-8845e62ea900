import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { useNavigation } from "@react-navigation/native";
import { Reservation } from "../../interfaces/reservation";
import { Card, Col, Row, Section } from "../main";
import { theme } from "../../theme/theme";
import { PROPERTY_SCREENS } from "../../navigation/constants";
import { PropertyStackNavigationProp } from "../../navigation/types";
import { PartialProperty } from "../../interfaces/property";
import { getReservationStatusData } from "../../utils/convertions";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import {
  formatDateDMY,
  getStringTimeFromIso,
} from "../../utils/date-time.utils";

interface ReservationsSectionProps {
  reservations: Reservation[];
  property: PartialProperty;
}

export const ReservationsSection: React.FC<ReservationsSectionProps> = ({
  reservations,
  property,
}) => {
  const navigation = useNavigation<PropertyStackNavigationProp>();

  if (!reservations?.length) {
    return (
      <Section title="Reservaciones">
        <Text style={styles.noDataText}>No hay reservaciones registradas</Text>
      </Section>
    );
  }

  return (
    <Section title="Reservaciones">
      <>
        {reservations.map((reservation) => {
          const reservationStatusData = getReservationStatusData(
            reservation.status
          );

          return (
            <TouchableOpacity
              key={reservation.id}
              onPress={() =>
                navigation.navigate(PROPERTY_SCREENS.RESERVATION_DETAIL, {
                  reservation,
                })
              }
              activeOpacity={0.7}
            >
              <Card
                style={[
                  styles.reservationCard,
                  { borderColor: reservationStatusData.color },
                ]}
              >
                <Row align="flex-start">
                  <Col style={styles.reservationInfo}>
                    <Row align="center" style={styles.headerRow}>
                      <Text style={styles.facilityName} numberOfLines={1}>
                        {reservation.facility.name}
                      </Text>
                      <View
                        style={[
                          styles.statusChip,
                          {
                            backgroundColor: reservationStatusData.color,
                          },
                        ]}
                      >
                        <MaterialCommunityIcons
                          name={reservationStatusData.icon}
                          size={12}
                          color={theme.colors.white}
                        />
                        <Text style={styles.statusText}>
                          {reservationStatusData.label}
                        </Text>
                      </View>
                    </Row>

                    <Row align="center" style={styles.detailRow}>
                      <MaterialCommunityIcons
                        name="calendar"
                        size={16}
                        color={theme.colors.gray500}
                      />
                      <Text style={styles.dateText}>
                        {formatDateDMY(reservation.startDateTime)}
                      </Text>
                    </Row>

                    <Row align="center" style={styles.detailRow}>
                      <MaterialCommunityIcons
                        name="clock"
                        size={16}
                        color={theme.colors.gray500}
                      />
                      <Text style={styles.timeText}>
                        {getStringTimeFromIso(reservation.startDateTime)} -{" "}
                        {getStringTimeFromIso(reservation.endDateTime)}
                      </Text>
                    </Row>

                    <Row align="center" style={styles.detailRow}>
                      <MaterialCommunityIcons
                        name="account-multiple"
                        size={16}
                        color={theme.colors.gray500}
                      />
                      <Text style={styles.peopleText}>
                        {reservation.amountOfPeople}{" "}
                        {reservation.amountOfPeople === 1
                          ? "persona"
                          : "personas"}
                      </Text>
                    </Row>

                    {reservation.deniedReason && (
                      <View style={styles.deniedReasonContainer}>
                        <MaterialCommunityIcons
                          name="information"
                          size={14}
                          color={theme.colors.error}
                        />
                        <Text style={styles.deniedReasonText} numberOfLines={2}>
                          {reservation.deniedReason}
                        </Text>
                      </View>
                    )}
                  </Col>
                  <MaterialCommunityIcons
                    name="chevron-right"
                    size={theme.fontSizes.lg}
                    color={theme.colors.gray500}
                  />
                </Row>
              </Card>
            </TouchableOpacity>
          );
        })}
      </>
    </Section>
  );
};

const styles = StyleSheet.create({
  noDataText: {
    textAlign: "center",
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    padding: theme.spacing.lg,
    fontStyle: "italic",
  },
  reservationCard: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: theme.spacing.md,
    padding: theme.spacing.md,
    borderRadius: theme.radii.md,
    borderLeftWidth: 4,
  },
  reservationInfo: {
    flex: 1,
    paddingLeft: theme.spacing.md,
  },
  headerRow: {
    justifyContent: "space-between",
    marginBottom: theme.spacing.sm,
  },
  facilityName: {
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
    color: theme.colors.black,
    flex: 1,
    marginRight: theme.spacing.sm,
  },
  statusChip: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.radii.sm,
  },
  statusText: {
    fontSize: theme.fontSizes.xs,
    fontWeight: "600",
    color: theme.colors.white,
    marginLeft: 4,
    textTransform: "capitalize",
  },
  detailRow: {
    marginBottom: theme.spacing.xs,
  },
  dateText: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
    marginLeft: 6,
  },
  timeText: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
    marginLeft: 6,
  },
  peopleText: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
    marginLeft: 6,
  },
  deniedReasonContainer: {
    flexDirection: "row",
    alignItems: "flex-start",
    backgroundColor: `${theme.colors.error}10`,
    padding: theme.spacing.sm,
    borderRadius: theme.radii.sm,
    marginTop: theme.spacing.sm,
  },
  deniedReasonText: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.error,
    marginLeft: 6,
    flex: 1,
    lineHeight: 16,
  },
});
