import { View, Text, StyleSheet, TouchableOpacity } from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { theme } from "../../theme";
import { PartialProperty } from "../../interfaces/property";
import { getPropertyIcon, getPropertyTypeLabel } from "../../utils/convertions";

interface PropertySelectorProps {
  properties: PartialProperty[];
  selectedPropertyId: string | null;
  onPropertySelect: (propertyId: string) => void;
}

export const PropertySelector: React.FC<PropertySelectorProps> = ({
  properties,
  selectedPropertyId,
  onPropertySelect,
}) => {
  return (
    <View style={styles.container}>
      <View style={styles.propertiesContainer}>
        {properties.map((property) => {
          const isSelected = property.id === selectedPropertyId;
          return (
            <TouchableOpacity
              key={property.id}
              style={[
                styles.propertyCard,
                isSelected && styles.selectedPropertyCard,
              ]}
              onPress={() => onPropertySelect(property.id)}
            >
              <View style={styles.propertyHeader}>
                <MaterialCommunityIcons
                  name={getPropertyIcon(property.type)}
                  size={theme.fontSizes.lg}
                  color={isSelected ? theme.colors.white : theme.colors.primary}
                />
                <Text
                  style={[
                    styles.propertyType,
                    isSelected && styles.selectedText,
                  ]}
                >
                  {getPropertyTypeLabel(property.type)}
                </Text>
              </View>
              <Text
                style={[
                  styles.propertyAddress,
                  isSelected && styles.selectedText,
                ]}
                numberOfLines={2}
                ellipsizeMode="tail"
              >
                {property.address}
              </Text>
              {isSelected && (
                <View style={styles.selectedIndicator}>
                  <MaterialCommunityIcons
                    name="check-circle"
                    size={theme.fontSizes.md}
                    color={theme.colors.white}
                  />
                </View>
              )}
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: theme.spacing.md,
    marginBottom: theme.spacing.xs,
  },
  title: {
    fontSize: theme.fontSizes.lg,
    fontWeight: "600",
    color: theme.colors.primary,
    marginBottom: theme.spacing.md,
    textAlign: "center",
  },
  propertiesContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  propertyCard: {
    backgroundColor: theme.colors.white,
    borderRadius: theme.radii.lg,
    paddingVertical: theme.spacing.md,
    marginBottom: theme.spacing.sm,
    width: "48%",
    minHeight: 100,
    borderWidth: 2,
    borderColor: theme.colors.primaryLight,
    position: "relative",
    alignItems: "center",
  },
  selectedPropertyCard: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primaryDark,
  },
  propertyHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: theme.spacing.sm,
  },
  propertyType: {
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
    color: theme.colors.primary,
    padding: theme.spacing.sm,
  },
  propertyAddress: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray700,
    lineHeight: 18,
  },
  selectedText: {
    color: theme.colors.white,
  },
  selectedIndicator: {
    position: "absolute",
    top: theme.spacing.xs,
    right: theme.spacing.xs,
  },
});
