# Navegación de la Aplicación

Esta carpeta contiene la nueva estructura de navegación modernizada para la aplicación ResApp.

## Estructura

```
src/navigation/
├── index.ts              # Exportaciones principales
├── types.ts              # Tipos TypeScript para navegación
├── constants.ts          # Constantes de rutas y configuración
├── TabNavigator.tsx      # Navegador de tabs principal
├── AppNavigator.tsx      # Navegador raíz de la aplicación
├── AuthNavigator.tsx     # Navegador de autenticación
├── MainNavigator.tsx     # Navegador principal (post-login)
└── stacks/               # Stacks de navegación individuales
    ├── DashboardStack.tsx
    ├── FacilitiesStack.tsx
    ├── PropertyStack.tsx
    ├── PaymentsStack.tsx
    └── AccountStack.tsx
```

## Características Principales

### 1. Tipado TypeScript Mejorado
- Tipos centralizados en `types.ts`
- Parámetros de rutas bien definidos
- Mejor autocompletado y detección de errores

### 2. Constantes Centralizadas
- Nombres de tabs y pantallas en `constants.ts`
- Configuración de iconos
- Rutas donde ocultar la tab bar

### 3. Stacks Modernizados
- Animaciones mejoradas
- Configuración consistente
- Mejor organización de pantallas

### 4. Tab Navigator Actualizado
- Uso de constantes en lugar de strings hardcodeados
- Mejor gestión del estado de carga
- Iconos dinámicos basados en configuración

## Tabs Disponibles

1. **Dashboard** - Pantalla principal con resumen
2. **Amenidades** - Gestión de amenidades y reservaciones
3. **Propiedad** - Información de la propiedad del usuario
4. **Pagos** - Gestión de pagos y facturas
5. **Cuenta** - Perfil del usuario y configuración

## Uso

### Navegación entre tabs
```typescript
import { TAB_NAMES } from '../navigation/constants';

// Navegar a un tab específico
navigation.navigate(TAB_NAMES.FACILITIES);
```

### Navegación dentro de un stack
```typescript
import { FACILITIES_SCREENS } from '../navigation/constants';

// Navegar a una pantalla específica dentro del stack
navigation.navigate(FACILITIES_SCREENS.FACILITY_DETAIL, { facility });
```

### Tipos de rutas
```typescript
import { FacilityRouteType } from '../navigation/types';

const route = useRoute<FacilityRouteType>();
const { facility } = route.params;
```

## Migración desde la versión anterior

La nueva estructura mantiene compatibilidad hacia atrás exportando los nombres antiguos:
- `MainStack` → `DashboardStack`
- `PropertiesStack` → `PropertyStack`

## Extensibilidad

Para agregar nuevas pantallas:

1. Actualizar los tipos en `types.ts`
2. Agregar constantes en `constants.ts`
3. Actualizar el stack correspondiente
4. Agregar la pantalla al stack navigator
