import { NavigatorScreenParams, RouteProp } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { Facility } from "../interfaces/facility";
import { Fine } from "../interfaces/fine";
import { Infraction } from "../interfaces/infraction";
import { Complaint } from "../interfaces/complaint";
import { Reservation } from "../interfaces/reservation";
import { MaintenanceIssueReport } from "../interfaces/maintenance-issue-report";
import { MonthlyMaintenanceCharge } from "../interfaces/monthly-maintenance-charge";
import { Pet } from "../interfaces/pet";
import { Vehicle } from "../interfaces/vehicle";
import { ParkingSpot } from "../interfaces/parking-spot";
import {
  PartialMaintenanceIssueReport,
  Property,
} from "../interfaces/property";
import { User } from "../interfaces/user";

// Stack Parameter Lists
export type DashboardStackParamList = {
  Dashboard: undefined;
  CreateComplaint: { complaintId?: string };
  CreateMaintenanceReport: { reportId?: string };
  EmergencyNumbers: undefined;
};

export type FacilitiesStackParamList = {
  FacilitiesList: { isLoading?: boolean };
  FacilityDetail: { facility: Facility };
  CreateReservation: {
    id: Facility["id"];
    name: Facility["name"];
    maxAmountOfPeople: Facility["maxAmountOfPeople"];
    maxTimeOfStay: Facility["maxTimeOfStay"];
    open: Facility["open"];
    close: Facility["close"];
    selectedDate: string;
  };
};

export type PropertyStackParamList = {
  PropertyDetail: undefined;
  PropertyFines: { fines: Fine[]; property: Property };
  PropertyInfractions: { infractions: Infraction[]; property: Property };
  PropertyReservations: {
    reservations: Reservation[];
    property: Property;
  };
  PropertyMaintenanceReports: {
    maintenanceIssueReports: PartialMaintenanceIssueReport[];
    property: Property;
  };
  PropertyComplaints: { complaints: Complaint[]; property: Property };
  PropertyTags: { propertyId: Property["id"] };
  PropertyResidents: {
    propertyId: Property["id"];
  };
  PropertyVehicles: { propertyId: Property["id"] };
  PropertyPets: { propertyId: Property["id"] };
  PropertyParkingSpots: {
    propertyId: Property["id"];
  };
  PropertyMonthlyCharges: { filterPaid?: boolean };
  // Detail screens
  ReservationDetail: { reservation: Reservation };
  FineDetail: { fine: Fine };
  InfractionDetail: { infraction: Infraction };
  MonthlyChargeDetail: { charge: MonthlyMaintenanceCharge };
  ComplaintDetail: { complaint: Complaint };
  MaintenanceIssueReportDetail: {
    report: MaintenanceIssueReport | PartialMaintenanceIssueReport;
  };
};

export type PaymentsStackParamList = {
  PaymentsList: undefined;
  PaymentDetail: { paymentId: string };
};

export type AccountStackParamList = {
  Profile: undefined;
  Settings: undefined;
  EditProfile: undefined;
};

// Main Tab Navigator Parameter List
export type MainTabParamList = {
  DashboardTab: NavigatorScreenParams<DashboardStackParamList>;
  FacilitiesTab: NavigatorScreenParams<FacilitiesStackParamList>;
  PropertyTab: NavigatorScreenParams<PropertyStackParamList>;
  PaymentsTab: NavigatorScreenParams<PaymentsStackParamList>;
  AccountTab: NavigatorScreenParams<AccountStackParamList>;
};

// Root Stack Parameter List
export type RootStackParamList = {
  Auth: undefined;
  Main: NavigatorScreenParams<MainTabParamList>;
};

// Specific Route Types
export type FacilityRouteProp = RouteProp<
  FacilitiesStackParamList,
  "FacilityDetail"
>;

export type CreateReservationRouteProp = RouteProp<
  FacilitiesStackParamList,
  "CreateReservation"
>;

export type PropertyComplaintsRouteProp = RouteProp<
  PropertyStackParamList,
  "PropertyComplaints"
>;

export type PropertyFinesRouteProp = RouteProp<
  PropertyStackParamList,
  "PropertyFines"
>;

export type PropertyInfractionsRouteProp = RouteProp<
  PropertyStackParamList,
  "PropertyInfractions"
>;

export type PropertyReservationsRouteProp = RouteProp<
  PropertyStackParamList,
  "PropertyReservations"
>;

export type PropertyMaintenanceReportsRouteProp = RouteProp<
  PropertyStackParamList,
  "PropertyMaintenanceReports"
>;

export type PropertyMonthlyChargesRouteProp = RouteProp<
  PropertyStackParamList,
  "PropertyMonthlyCharges"
>;

export type PropertyResidentsRouteProp = RouteProp<
  PropertyStackParamList,
  "PropertyResidents"
>;

export type PropertyVehiclesRouteProp = RouteProp<
  PropertyStackParamList,
  "PropertyVehicles"
>;

export type PropertyPetsRouteProp = RouteProp<
  PropertyStackParamList,
  "PropertyPets"
>;

export type PropertyParkingSpotsRouteProp = RouteProp<
  PropertyStackParamList,
  "PropertyParkingSpots"
>;

// Detail screen route props
export type ReservationDetailRouteProp = RouteProp<
  PropertyStackParamList,
  "ReservationDetail"
>;

export type FineDetailRouteProp = RouteProp<
  PropertyStackParamList,
  "FineDetail"
>;

export type InfractionDetailRouteProp = RouteProp<
  PropertyStackParamList,
  "InfractionDetail"
>;

export type MonthlyChargeDetailRouteProp = RouteProp<
  PropertyStackParamList,
  "MonthlyChargeDetail"
>;

export type ComplaintDetailRouteProp = RouteProp<
  PropertyStackParamList,
  "ComplaintDetail"
>;

export type MaintenanceIssueReportDetailRouteProp = RouteProp<
  PropertyStackParamList,
  "MaintenanceIssueReportDetail"
>;

// Navigation Props
export type PropertyStackNavigationProp =
  NativeStackNavigationProp<PropertyStackParamList>;
export type FacilitiesStackNavigationProp =
  NativeStackNavigationProp<FacilitiesStackParamList>;
export type DashboardStackNavigationProp =
  NativeStackNavigationProp<DashboardStackParamList>;
export type PaymentsStackNavigationProp =
  NativeStackNavigationProp<PaymentsStackParamList>;
export type AccountStackNavigationProp =
  NativeStackNavigationProp<AccountStackParamList>;
