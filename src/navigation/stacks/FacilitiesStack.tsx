import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { FacilityScreen } from "../../screens/Facilities/FacilityScreen";
import { FacilitiesStackParamList } from "../types";
import { FACILITIES_SCREENS } from "../constants";
import { FacilitiesScreenRedesigned } from "../../screens/Facilities/FacilitiesScreenRedesigned";

const Stack = createNativeStackNavigator<FacilitiesStackParamList>();

export const FacilitiesStack: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        animation: "slide_from_right",
      }}
    >
      <Stack.Screen
        name={FACILITIES_SCREENS.FACILITIES_LIST}
        component={FacilitiesScreenRedesigned}
        options={{
          title: "Amenidades",
          headerShown: false,
          headerBackVisible: true,
        }}
      />
      <Stack.Screen
        name={FACILITIES_SCREENS.FACILITY_DETAIL}
        component={FacilityScreen}
        options={{
          title: "Detalle de Amenidad",
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  );
};
