import { z } from "zod";

export type LoginFormValues = z.infer<typeof loginSchema>;

export const loginSchema = z.object({
  email: z
    .string({ required_error: "Correo requerido" })
    .email("Correo inválido"),
  password: z
    .string({ required_error: "Contraseña requerida" })
    .min(6, "Mínimo 6 caracteres"),
});

export type ReservationFormValues = z.infer<typeof reservationSchema>;

export const reservationSchema = z.object({
  amountOfPeople: z
    .string({ required_error: "Cantidad de personas requerida" })
    .transform((val) => Number(val))
    .refine((val) => !isNaN(val) && val >= 1, {
      message: "1 persona mínimo.",
    }),
  hoursRequested: z
    .string({ required_error: "Horas solicitadas requeridas" })
    .transform((val) => Number(val))
    .refine((val) => !isNaN(val) && val >= 1, {
      message: "1 hora mínimo.",
    }),
  selectedTimeRange: z
    .string({ required_error: "Selecciona un horario" })
    .min(1, "Selecciona un horario"),
});
