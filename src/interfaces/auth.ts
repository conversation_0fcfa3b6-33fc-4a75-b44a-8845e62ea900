export interface ConfirmPasswordRequest extends PasswordConfirmationValues {
  token: string;
}

export interface ConfirmPasswordResponse {
  message: string;
}

export interface PasswordConfirmationValues {
  password: string;
  passwordConfirmation: string;
}

export interface AuthRequest {
  email: string;
  password: string;
}

export interface AuthResponse {
  firstName: string;
  lastName: string;
  roles: string[];
}
