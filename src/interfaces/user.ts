import { News } from "./news";
import { Property } from "./property";
import { Task } from "./task";
import { Complaint } from "./complaint";
import { Role } from "./role";
import { Visit } from "./visit";
import { Reservation } from "./reservation";

export interface User {
  id: string;
  email: string;
  firstName: string;
  paternalLastName: string;
  maternalLastName: string;
  phone: string;
  createdAt: Date;
  updatedAt: Date;
  newsItems: News[];
  properties: Property[];
  complaints: Complaint[];
  tasks: Task[];
  roles: Role[];
  visits: Visit[];
  reservations: Reservation[];
  property: Property;
}
