import { User } from "./user";
import { Role } from "./role";
import { Announcement } from "./announcement";

export interface Me {
  id: User["id"];
  email: User["email"];
  firstName: User["firstName"];
  paternalLastName: User["paternalLastName"];
  maternalLastName: User["maternalLastName"];
  phone: User["phone"];
  announcements: PartialAnnouncement[];
  properties: { id: string; address: string; type: string }[];
}

export interface PartialAnnouncement {
  id: Announcement["id"];
  title: Announcement["title"];
  message: Announcement["message"];
  images: AnnouncementImage[];
  imageUrl: Announcement["imageUrl"];
  createdAt: Announcement["createdAt"];
  role: PartialRole;
}

interface PartialRole {
  name: Role["name"];
}

export interface AnnouncementImage {
  id: string;
  path: string;
  announcementId: string;
  createdAt: Date;
}
