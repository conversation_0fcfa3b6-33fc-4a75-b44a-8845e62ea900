import { ParkingSpot } from "./parking-spot";
import { Property } from "./property";

export interface Visit {
  id: string;
  visitorName: string;
  visitMethod: VisitMethod;
  vehiclePlate?: string;
  schedule: string;
  checkInTime: string;
  checkOutTime?: string;
  qrCode?: string;
  isQrUsed: boolean;
  propertyId: Property["id"];
  parkingSpotId?: ParkingSpot["id"];
}

export enum VisitMethod {
  WALKING = "WALKING",
  CAR = "CAR",
}
