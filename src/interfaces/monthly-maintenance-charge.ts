import { Payment } from "./payment";
import { MaintenanceFee } from "./maintenance-fee";
import { Property } from "./property";

export interface MonthlyMaintenanceCharge {
  id: string;
  propertyId: string;
  maintenanceFeeId: string;
  month: number;
  year: number;
  dueDate: string;
  isPaid: boolean;
  paidAt?: string;
  lateFeeApplied: boolean;
  lateFeeAmount?: number;
  waivedLateFee: boolean;
  createdAt: string;
  updatedAt: string;
  property?: Property;
  maintenanceFee?: MaintenanceFee;
  payment?: Payment;
}
