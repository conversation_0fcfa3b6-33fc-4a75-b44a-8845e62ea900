import { Property } from "./property";
import { User } from "./user";
import { Facility } from "./facility";

export enum Status {
  OPEN = "OPEN",
  IN_PROGRESS = "IN_PROGRESS",
  RESOLVED = "RESOLVED",
}

export interface MaintenanceIssueReport {
  id: string;
  description: string;
  status: Status;
  createdAt: string;
  updatedAt: string;
  propertyId?: Property["id"];
  reportedBy: User["id"];
  facilityId: Facility["id"];
  images: MaintenanceIssueReportImage[];
}

export interface MaintenanceIssueReportImage {
  id: string;
  path: string;
  reportId: string;
  maintenanceIssueReportId: string;
  createdAt: Date;
}

export interface CreateMaintenanceIssueReport {
  description: string;
  propertyId: string;
  images: string[];
}
